batch_size: 1
iters: 160000

model:
  type: MLATransformer
  pretrained: pretrain/pretrained_swin.pdparams
  backbone:
    type: SwinTransformer_base_patch4_window7_224
    ape: False
    drop_path_rate: 0.3
    patch_norm: True
  num_classes: 26
  in_channels: [128, 256, 512, 1024]
  mlahead_channels: 128

train_dataset:
  type: AutoNue
  dataset_root: data/IDD_Segmentation
  transforms:
    - type: Resize
      target_size: [1920, 1080]
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0.25
    - type: RandomPaddingCrop
      crop_size: [1024, 512]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.25
      brightness_prob: 1
      contrast_range: 0.25
      contrast_prob: 1
      saturation_range: 0.25
      saturation_prob: 1
      hue_range: 63
      hue_prob: 1
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: train


val_dataset:
  type: AutoNue
  dataset_root: data/IDD_Segmentation
  transforms:
    - type: Resize
      target_size: [256, 256] #[1920, 1080]
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: val


optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 0.0001

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.005
  end_lr: 0
  power: 2

iters: 160000

loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
  coef: [1, 0.4]
