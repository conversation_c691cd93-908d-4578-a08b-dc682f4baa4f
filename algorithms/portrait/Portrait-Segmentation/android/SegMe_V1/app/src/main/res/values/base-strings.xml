<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright 2013 The Android Open Source Project

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<resources>
    <string name="app_name">SegMe</string>
    <string name="intro_message">
        <![CDATA[


            This sample demonstrates the basic use of TfLite API. Check the source code to see how
            you can use TfLite for efficient, on-device inference with trained TensorFlow models.


        ]]>
    </string>
    <string name="threads">Threads:</string>
</resources>
