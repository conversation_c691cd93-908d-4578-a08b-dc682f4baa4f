===========================ptq_params===========================
model_name:ocrnet_hrnetw18_KL
python:python3.7
##
--model_dir:test_tipc/output/ocrnet_hrnetw18_KL/ocrnet_hrnetw18_cityscapes_1024x512_160k
##
--config:test_tipc/configs/ocrnet_hrnetw18/ocrnet_hrnetw18_cityscapes_1024x512_160k.yml
--batch_num:1
--batch_size:1
##
trainer:PTQ
PTQ:slim/quant/ptq.py
##
===========================infer_params===========================
inference:deploy/python/infer.py
--device:cpu|gpu
--batch_size:1
--config:quant_model/deploy.yaml
--image_path:test_tipc/cpp/cityscapes_demo.png
--benchmark:True