# Copyright (c) 2021 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import os.path as osp

from qtpy.QtWidgets import QWidget, QLabel, QHBoxLayout
from qtpy.QtGui import QIcon, QMovie
from qtpy import QtCore

from eiseg import pjpath


class LoadingWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(QtCore.Qt.FramelessWindowHint)
        layout = QHBoxLayout(self)
        self.label = QLabel()
        layout.addWidget(self.label)
        self.setLayout(layout)
        self.movie = QMovie(osp.join(pjpath, "resource", "loading.gif"))
        self.label.setMovie(self.movie)
        self.movie.start()
