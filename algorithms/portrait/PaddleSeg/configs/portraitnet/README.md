# PortraitNet: Real-time Portrait Segmentation Network for Mobile Device

## Reference

> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. "PortraitNet: Real-time portrait segmentation network for mobile device". @ CAD&Graphics 2019.


## Performance

| Model | Backbone | Dataset | Resolution | Training Iters | mIoU | Link |
|-|-|-|-|-|-|-|
|PortraitNet|MobileNetV2|EG1800|224x224|46000|96.92%|[model](https://bj.bcebos.com/paddleseg/dygraph/portraitnet_mobilenetv2_eg1800_224x224_46k/model.pdparams)|
|PortraitNet|MobileNetV2|Supervise.ly|224x224|60000|93.94%|[model](https://bj.bcebos.com/paddleseg/dygraph/portraitnet_mobilenetv2_supervisely_224x224_60k/model.pdparams)|

## Online Tutorial
[AI Studio](https://aistudio.baidu.com/aistudio/projectdetail/1754799)

## Dataset
The training process will automatically download the dataset.

You can also download and view it manually.

[<PERSON><PERSON> Pan](https://pan.baidu.com/s/15uBpR7zFF2zpUccoq5pQYg)

password: ajcs
