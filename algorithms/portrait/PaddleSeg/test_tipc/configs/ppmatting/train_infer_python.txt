===========================train_params===========================
model_name:ppmatting
python:python3.7
gpu_list:0|0,1
Global.use_gpu:null|null
--precision:null
--iters:lite_train_lite_infer=20|lite_train_whole_infer=20|whole_train_whole_infer=1000
--save_dir:
--batch_size:lite_train_lite_infer=2|lite_train_whole_infer=2|whole_train_whole_infer=8
--model_path:null
train_model_name:best_model/model.pdparams
train_infer_img_dir:test_tipc/data/PPM-100/val.txt
null:null
##
trainer:norm
norm_train:Matting/tools/train.py --config test_tipc/configs/ppmatting/modnet_mobilenetv2.yml --do_eval --save_interval 500 --seed 100
pact_train:null
fpgm_train:null
distill_train:null
null:null
null:null
##
===========================eval_params===========================
eval:null
null:null
##
===========================export_params===========================
--save_dir:
--model_path:
norm_export:Matting/tools/export.py --config test_tipc/configs/ppmatting/modnet_mobilenetv2.yml
quant_export:null
fpgm_export:null
distill_export:null
export1:null
export2:null
===========================infer_params===========================
infer_model:./test_tipc/output/ppmatting/modnet-mobilenetv2.pdparams
infer_export:Matting/tools/export.py --config test_tipc/configs/ppmatting/modnet_mobilenetv2.yml
infer_quant:False
inference:Matting/deploy/python/infer.py
--device:cpu|gpu
--enable_mkldnn:True
--cpu_threads:6
--batch_size:1
--use_trt:False
--precision:fp32
--config:
--image_path:./test_tipc/data/PPM-100/val.txt
--save_log_path:null
--benchmark:True
--save_dir:
--model_name:ppmatting
===========================infer_benchmark_params==========================
random_infer_input:[{float32,[3,512,512]}]
