简体中文 | [English](losses_en.md)
# 损失函数的详细说明

* ## [paddleseg.models.losses.binary_cross_entropy_loss](./BCELoss_cn.md)

* ## [paddleseg.models.losses.bootstrapped_cross_entropy](./BootstrappedCrossEntropyLoss_cn.md)

* ## [paddleseg.models.losses.cross_entropy_loss](./CrossEntropyLoss_cn.md)

* ## [paddleseg.models.losses.decoupledsegnet_relax_boundary_loss](./RelaxBoundaryLoss_cn.md)

* ## [paddleseg.models.losses.dice_loss](./DiceLoss_cn.md)

* ## [paddleseg.models.losses.DualTaskLoss](./DualTaskLoss_cn.md)

* ## [paddleseg.models.losses.edge_attention_loss](./EdgeAttentionLoss_cn.md)

* ## [paddleseg.models.losses.l1_loss](./L1Loss_cn.md)

* ## [paddleseg.models.losses.Lovasz_loss](./lovasz_loss_cn.md)

* ## [paddleseg.models.losses.MSELoss](./MSELoss_cn.md)

* ## [paddleseg.models.losses.ohem_cross_entropy_loss](./OhemCrossEntropyLoss_cn.md)

* ## [paddleseg.models.losses.ohem_edge_attention_loss](./OhemEdgeAttentionLoss_cn.md)

* ## [paddleseg.models.losses.semantic_connectivity_loss](./SemanticConnectivityLoss_cn.md)
