# ENet: A Deep Neural Network Architecture forReal-Time Semantic Segmentation

## Reference
> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. "ENet: A Deep Neural Network Architecture for
Real-Time Semantic Segmentation." arXiv preprint arXiv:1606.02147(2016).

## Performance

### Cityscapes

| Model | Backbone | Resolution | Training Iters | mIoU | mIoU (flip) | mIoU (ms+flip) | Links |
|:-:|:-:|:-:|:-:|:-:|:-:|:-:|:-:|
|ENet|-|1024x512|80000|67.42%|68.11%|67.99%|[model](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/enet_cityscapes_1024x512_80k/model.pdparams)\|[log](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/enet_cityscapes_1024x512_80k/train.log)\|[vdl](https://paddlepaddle.org.cn/paddle/visualdl/service/app?id=5d57386cdfcdb6a6bcb5135af134a0f2)|
