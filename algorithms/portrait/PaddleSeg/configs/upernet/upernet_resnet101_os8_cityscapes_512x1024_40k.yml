_base_: '../_base_/cityscapes.yml'

batch_size: 2
iters: 40000

model:
  type: UPerNet
  backbone:
    type: ResNet101_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet101_vd_ssld.tar.gz
  backbone_indices: [0, 1, 2, 3]
  channels: 512
  dropout_prob: 0.1
  enable_auxiliary_loss: True

optimizer:
  type: sgd
  weight_decay: 0.0005

loss:
  types:
    - type: CrossEntropyLoss
  types:
    - type: CrossEntropyLoss
  coef: [1, 0.4]


lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  end_lr: 0.0
  power: 0.9