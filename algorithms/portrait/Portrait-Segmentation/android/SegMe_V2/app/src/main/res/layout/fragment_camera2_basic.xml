<?xml version="1.0" encoding="utf-8"?><!--
 Copyright 2014 The Android Open Source Project

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#bb7700">

    <com.example.android.tflitecamerademo4.AutoFitTextureView
        android:id="@+id/texture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <jp.co.cyberagent.android.gpuimage.GPUImageView
        android:id="@+id/gpuimageview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/details"
        android:scaleType="fitStart"
        app:gpuimage_show_loading="false"
        app:gpuimage_surface_type="texture_view" />

    <LinearLayout
        android:id="@+id/details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_info_view"
        android:layout_alignParentEnd="false"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="false"
        android:background="#bb7700"
        android:orientation="vertical"
        android:weightSum="100">

        <!--
        <ImageView
            android:id="@+id/logoview2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="30"
            android:scaleType="fitStart"
            android:src="@drawable/logo" />
            -->

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="30"
            android:background="#EAEAEA"
            android:max="100"
            android:progress="50"
            android:scaleType="fitStart" />

        <TextView
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_weight="30"
            android:textColor="#FFF"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/bottom_info_view"
        android:layout_width="match_parent"
        android:layout_height="200dp"

        android:layout_alignParentBottom="true"
        android:layout_marginBottom="10dp"
        android:background="#513400"
        android:orientation="horizontal">

      <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
                android:layout_height="wrap_content"
            android:gravity="center"
                android:text="Threads"
            android:textAlignment="center"
            android:textColor="@android:color/white" />

        <NumberPicker
            android:id="@+id/np"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:theme="@style/AppTheme.Picker"
            android:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/modelLayout"
            android:layout_width="150dp"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:text="@string/filterLabel"
                android:textAlignment="center"
                android:textColor="@android:color/white" />

            <ListView
                android:id="@+id/model"
                android:layout_width="match_parent"
                android:layout_height="180dp">

            </ListView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/deviceLayout"
            android:layout_width="140dp"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:text="@string/deviceLabel"
                android:textAlignment="center"
                android:textColor="@android:color/white" />

            <ListView
                android:id="@+id/device"
                android:layout_width="match_parent"
                android:layout_height="180dp" >

            </ListView>

        </LinearLayout>

    </LinearLayout>

    <!--
    <ImageView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitStart"
        android:visibility="visible"
        app:srcCompat="@drawable/dock"
        tools:visibility="visible" />
        -->

</RelativeLayout>
