# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .dataset import Dataset
from .cityscapes import Cityscapes
from .voc import PascalVOC
from .ade import ADE20K
from .optic_disc_seg import OpticDiscSeg
from .pascal_context import <PERSON><PERSON>ontext
from .mini_deep_globe_road_extraction import MiniDeepGlobeRoadExtraction
from .eg1800 import EG1800
from .supervisely import SUPERVISELY
from .cocostuff import CocoStuff
from .stare import STARE
from .drive import DRIVE
from .hrf import HRF
from .chase_db1 import CHASEDB1
from .pp_humanseg14k import <PERSON><PERSON><PERSON><PERSON>eg14K
from .pssl import PSSLDataset
