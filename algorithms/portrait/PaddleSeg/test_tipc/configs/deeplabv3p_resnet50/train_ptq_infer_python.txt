===========================ptq_params===========================
model_name:deeplabv3p_resnet50_KL
python:python3.7
##
--model_dir:test_tipc/output/deeplabv3p_resnet50_KL/pp_humanseg_server_export_512x512
##
--config:test_tipc/configs/deeplabv3p_resnet50/deeplabv3p_resnet50_humanseg_512x512_mini_supervisely.yml
--batch_num:1
--batch_size:1
##
trainer:PTQ
PTQ:slim/quant/ptq.py
##
===========================infer_params===========================
inference:deploy/python/infer.py
--device:cpu|gpu
--batch_size:1
--config:quant_model/deploy.yaml
--image_path:test_tipc/cpp/humanseg_demo.jpg
--benchmark:True