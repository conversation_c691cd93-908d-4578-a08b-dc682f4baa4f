# ESPNetV2: A Light-weight, Power Efficient, and General Purpose Convolutional Neural Network

## Reference

> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. "Espnetv2: A light-weight, power efficient, and general purpose convolutional neural network." In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 9190-9200. 2019.

## Performance

### CityScapes

| Model | Backbone | Resolution | Training Iters | mIoU | Links |
|:---:|:---:|:---:|:---:|:---:|:---:|
|ESPNetV2|-|1024x512|120000|70.88%|[model](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/espnet_cityscapes_1024x512_120k/model.pdparams) \| [log](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/espnet_cityscapes_1024x512_120k/train.log) \|[vdl](https://www.paddlepaddle.org.cn/paddle/visualdl/service/app/scalar?id=c717bd8c2b5a083de759492158c14ffd)


#### Additional Requirement
- paddlepaddle develop version after 20211230
