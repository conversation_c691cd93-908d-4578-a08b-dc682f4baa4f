{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "gpucom.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "code", "metadata": {"id": "hVvicP6qaVIh", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 98}, "outputId": "85f73ca6-6e5f-4977-fdd3-fd91490353f0"}, "source": ["from keras.layers import Input, Dense, Conv2D, Reshape, Lambda\n", "from keras.models import Model\n", "import numpy as np\n", "import tensorflow as tf\n", "import cv2, sys\n", "import imageio as io\n", "from matplotlib import pyplot as plt\n"], "execution_count": 1, "outputs": [{"output_type": "stream", "text": ["Using TensorFlow backend.\n"], "name": "stderr"}, {"output_type": "display_data", "data": {"text/html": ["<p style=\"color: red;\">\n", "The default version of TensorFlow in Colab will soon switch to TensorFlow 2.x.<br>\n", "We recommend you <a href=\"https://www.tensorflow.org/guide/migrate\" target=\"_blank\">upgrade</a> now \n", "or ensure your notebook will continue to use TensorFlow 1.x via the <code>%tensorflow_version 1.x</code> magic:\n", "<a href=\"https://colab.research.google.com/notebooks/tensorflow_version.ipynb\" target=\"_blank\">more info</a>.</p>\n"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {"tags": []}}]}, {"cell_type": "code", "metadata": {"id": "fJMWuk5B4anL", "colab_type": "code", "colab": {}}, "source": ["def decode_float(fnum):\n", "      \n", "      # split int and dec parts\n", "      fract,integ=np.modf(fnum)\n", "      \n", "      intstr=bin(np.int(integ))[2:]\n", "      floatstr=''\n", "\n", "      while fnum!=0.0:\n", "          fnum=fract*2\n", "          fract,integ=np.modf(fnum)\n", "          floatstr=floatstr+str(np.int(integ))\n", "          fnum=fract\n", "\n", "      # pad zeros to adjust length\n", "      intstr = (8-len(intstr))*'0' + intstr\n", "      floatstr += (8-len(floatstr))*'0'\n", "      \n", "      return intstr+floatstr\n", "\n", "def float2bin(im,stride=16):\n", "    \n", "     # Compute size of extracted output image\n", "     h,w=im.shape[0], im.shape[1]\n", "     result=np.zeros((h,w*stride), dtype=np.uint8)\n", "     \n", "     # 2D convolution with stride of 16 [kernel shape: (1,16)]\n", "     for i in range(0,h):\n", "         for j in range(0,w):\n", "             ridx=j*stride\n", "             bval=decode_float(im[i,j])\n", "             result[i,ridx:ridx+stride]=np.uint8(list(bval))*np.uint8([255])\n", "             #print(\"BBVAL: \",bval,im[i,j], (i,ridx) )\n", "     return result"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "T1vIJ0IYfOk2", "colab_type": "code", "colab": {}}, "source": ["# Identity kernel (testing)\n", "def kernel_init1(shape, dtype=None):\n", "    \n", "    kernel = np.zeros(shape)\n", "    kernel[:,:,0,0] = np.array([[0,0,0],[0,1,0],[0,0,0]])\n", "    return kernel\n", "\n", "# Fixed point fp16 kernel (compression)\n", "def kernel_init2(shape, dtype=None):\n", "  \n", "    base=np.array([7,6,5,4,3,2,1,0,-1,-2,-3,-4,-5,-6,-7,-8],dtype=np.float32)\n", "    kernel=np.power(2,base).reshape(shape)\n", "    return kernel"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "wP1sdKTZddZ1", "colab_type": "code", "colab": {}}, "source": ["# Test model 1 (Identity)\n", "inputs = Input(shape=(256,256,1))\n", "outputs = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(inputs)\n", "model1 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model1.summary()\n", "model1.save('model1.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "JVGmju5pihPk", "colab_type": "code", "colab": {}}, "source": ["# Test model 2 (Compression)\n", "inputs = Input(shape=(256,256,1))\n", "hidden = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(inputs)\n", "outputs = Conv2D(1, (1, 16), kernel_initializer=kernel_init2,strides=(1,16), padding=\"same\" )(hidden)\n", "model2 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model2.summary()\n", "model2.save('model2.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "0CWLDgD0aNU4", "colab_type": "code", "colab": {}}, "source": ["# Test model 3 (Channel_4X)\n", "inputs = Input(shape=(256,256,4))\n", "outputs = Conv2D(4, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(inputs)\n", "model1 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model1.summary()\n", "model1.save('model3.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "VmnBZ7L-a-aZ", "colab_type": "code", "colab": {}}, "source": ["# Test model 4 (Comp_Ch4X)\n", "inputs = Input(shape=(256,256,3))\n", "hidden1 = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(inputs)\n", "hidden2 = Conv2D(1, (1, 16), kernel_initializer=kernel_init2,strides=(1,16), padding=\"same\" )(hidden1)\n", "outputs= Reshape((256,16))(hidden2)\n", "model2 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model2.summary()\n", "model2.save('model4.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "MCV3GuZri2kC", "colab_type": "code", "colab": {}}, "source": ["# Test model 5 (Comp_ResCh4X)\n", "inputs=Input(shape=(196608,))\n", "reshape = Reshape((256,256,3))(inputs)\n", "hidden1 = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(reshape)\n", "hidden2 = Conv2D(1, (1, 16), kernel_initializer=kernel_init2,strides=(1,16), padding=\"same\" )(hidden1)\n", "outputs= Reshape((256,16))(hidden2)\n", "model2 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model2.summary()\n", "model2.save('model5.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "UOFflxTsG0I2", "colab_type": "code", "colab": {}}, "source": ["# Test model 6 (Comp_SliCh4X)\n", "inputs = Input(shape=(256,256,4))\n", "str_slice=Lambda(lambda inputs: tf.strided_slice(inputs, [0,0, 0, 0], [1,256, 256, 3], [1, 1, 1, 1]))(inputs)\n", "hidden1 = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(str_slice)\n", "hidden2 = Conv2D(1, (1, 16), kernel_initializer=kernel_init2,strides=(1,16), padding=\"same\" )(hidden1)\n", "outputs= Reshape((256,16))(hidden2)\n", "model2 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model2.summary()\n", "model2.save('model6.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "cufdbvqLB7_B", "colab_type": "code", "colab": {}}, "source": ["# Test model 7 (Comp_PadCh4X)\n", "inputs = Input(shape=(256,256,4))\n", "str_slice=Lambda(lambda inputs: tf.strided_slice(inputs, [0,0, 0, 0], [1,256, 256, 3], [1, 1, 1, 1]))(inputs)\n", "hidden = Conv2D(1, (3, 3), kernel_initializer=kernel_init1, padding=\"same\" )(str_slice)\n", "outputs=Lambda(lambda inputs: tf.pad(inputs, tf.constant([[0,0], [0,0], [0,0], [0,3]])))(hidden)\n", "model2 = Model(inputs=inputs, outputs=outputs)\n", "\n", "model2.summary()\n", "model2.save('model7.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "sN51vycRklji", "colab_type": "code", "colab": {}}, "source": ["# Read input and preprocess\n", "input_img = io.imread('https://drive.google.com/uc?id=1LAfmxWqXVUki71Ha_mWbQ4yrGIOTUGHV')\n", "input_img=input_img[:,:,0]\n", "img=np.float32(input_img/255.0)\n", "img=img.reshape((1,256,256,1))"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "qqHOTN_xlHdA", "colab_type": "code", "colab": {}}, "source": ["# Perform prediction\n", "out1=model1.predict(img)\n", "out2=model2.predict(img)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "urikIKo-lhe9", "colab_type": "code", "colab": {}}, "source": ["# Plot output of model1\n", "plt.imshow(out1.squeeze())"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "xnJiSuOUnvm4", "colab_type": "code", "colab": {}}, "source": ["output_img=float2bin(out2.squeeze()) # Extracted image [UINT8]\n", "\n", "print(\"\\nConverting the extracted image back to UINT8...\")\n", "\n", "print(\"\\nExtracted image:- \")\n", "print(\"Shape: \",output_img.shape)\n", "print(\"Type: \", output_img.dtype)\n", "print(\"Size: \",sys.getsizeof(output_img))\n", "print(\"Unique values: \", np.unique(output_img).shape[0])\n", "\n", "# Check if image was successfully extracted\n", "if np.array_equal(input_img,output_img):\n", "   print(\"\\nExtraction successful !!!\")\n", "else:\n", "   print(\"\\nExtraction failed !!!\")\n", "\n", "# Save the output image\n", "cv2.imwrite(\"decoded2.png\",output_img)\n", "plt.imshow(output_img)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "D_NStg14-X_Z", "colab_type": "code", "colab": {}}, "source": ["# Export mode1-1 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model1.h5',custom_objects={'kernel_init1':kernel_init1})\n", "tflite_model1 = converter.convert()\n", "open(\"model1.tflite\", \"wb\").write(tflite_model1)\n"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "jGlJRypD-dZO", "colab_type": "code", "colab": {}}, "source": ["# Export model-2 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model2.h5', custom_objects={'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model2.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "gINXeVuQafgh", "colab_type": "code", "colab": {}}, "source": ["# Export model-3 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model3.h5', custom_objects={'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model3.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "v8JNzsUrf75y", "colab_type": "code", "colab": {}}, "source": ["# Export model-4 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model4.h5', custom_objects={'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model4.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "QLGFwjjijvbR", "colab_type": "code", "colab": {}}, "source": ["# Export model-5 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model5.h5', custom_objects={'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model5.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "iebC5kqF4nx5", "colab_type": "code", "colab": {}}, "source": ["# Export model-6 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model6.h5', custom_objects={'tf':tf,'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model6.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "-9nO3XGpDjjx", "colab_type": "code", "colab": {}}, "source": ["# Export model-7 as tflite\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/model7.h5', custom_objects={'tf':tf,'kernel_init1':kernel_init1, 'kernel_init2':kernel_init2})\n", "tflite_model2 = converter.convert()\n", "open(\"model7.tflite\", \"wb\").write(tflite_model2)"], "execution_count": 0, "outputs": []}]}