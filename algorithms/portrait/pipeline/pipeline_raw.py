#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified Portrait Pipeline: Direct implementation using model APIs
Combines face detection, portrait segmentation, depth estimation, and bokeh effect
"""

import os
import sys
import time
import logging
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from pathlib import Path

# Add model paths to sys.path for direct imports
sys.path.append('/home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2')
sys.path.append('/home/<USER>/code/IEDemo/algorithms/portrait/Depth-Anything-V2')
sys.path.append('/home/<USER>/code/IEDemo/algorithms/portrait/BokehMe')
sys.path.append('/home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg')

# Import model components directly
from models.experimental import attempt_load
from utils.general import non_max_suppression, xyxy2xywh
from utils.torch_utils import select_device
from depth_anything_v2.dpt import DepthAnythingV2
from neural_renderer import ARNet, IUNet
from classical_renderer.scatter import ModuleRenderScatter

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplifiedPortraitPipeline:
    def __init__(self, bokeh_config=None):
        """Initialize all models and components"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")

        # Model paths
        self.yolo_weights = "/home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/preweight/preweight.pt"
        self.depth_model_path = "/home/<USER>/code/IEDemo/algorithms/portrait/Depth-Anything-V2/checkpoints/depth_anything_v2_vitl.pth"
        self.arnet_checkpoint = "/home/<USER>/code/IEDemo/algorithms/portrait/BokehMe/checkpoints/arnet.pth"
        self.iunet_checkpoint = "/home/<USER>/code/IEDemo/algorithms/portrait/BokehMe/checkpoints/iunet.pth"

        # Bokeh configuration with simplified parameters
        self.bokeh_config = {
            # Core bokeh parameters
            'K': 10.0,                          # blur parameter (default: 10)
            'gamma': 4.0,                       # gamma value (1~5, default: 4)
            'defocus_scale': 10.0,              # defocus scale (default: 10)

            # Depth processing control (simplified)
            'normalize_depth': True,            # True: min-max normalize to 0-1, False: keep original range

            # Focus control
            'auto_focus': True,                 # automatically calculate focus from face/portrait
            'manual_focus_depth': 0.5,          # manual focus depth (0~1, used when auto_focus=False)
            'focus_offset': 0.0,                # offset to add to calculated focus depth

            # Highlight enhancement
            'highlight': False,                 # enable highlight enhancement
            'highlight_RGB_threshold': 220/255, # RGB threshold for highlights
            'highlight_enhance_ratio': 0.4,     # enhancement ratio for highlights

            # Advanced parameters
            'gamma_min': 1.0,                   # minimum gamma for normalization
            'gamma_max': 5.0,                   # maximum gamma for normalization
        }

        # Update with user config
        if bokeh_config:
            self.bokeh_config.update(bokeh_config)

        # Initialize models
        self._load_models()

    def _load_models(self):
        """Load all required models"""
        logger.info("Loading models...")

        # Load YOLO face detection model
        self.yolo_model = attempt_load(self.yolo_weights, map_location=self.device)
        self.yolo_model.eval()

        # Load Depth-Anything-V2 model
        model_configs = {
            'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]}
        }
        self.depth_model = DepthAnythingV2(**model_configs['vitl'])
        self.depth_model.load_state_dict(torch.load(self.depth_model_path, map_location='cpu'))
        self.depth_model = self.depth_model.to(self.device).eval()

        # Load BokehMe models
        self.classical_renderer = ModuleRenderScatter().to(self.device)

        # ARNet configuration
        self.arnet = ARNet(
            shuffle_rate=2, in_channels=5, out_channels=4, middle_channels=128,
            num_block=3, share_weight=False, connect_mode='distinct_source',
            use_bn=False, activation='elu'
        ).to(self.device)

        # IUNet configuration
        self.iunet = IUNet(
            shuffle_rate=2, in_channels=8, out_channels=3, middle_channels=64,
            num_block=3, share_weight=False, connect_mode='distinct_source',
            use_bn=False, activation='elu'
        ).to(self.device)

        # Load BokehMe checkpoints
        checkpoint = torch.load(self.arnet_checkpoint)
        self.arnet.load_state_dict(checkpoint['model'])
        checkpoint = torch.load(self.iunet_checkpoint)
        self.iunet.load_state_dict(checkpoint['model'])

        self.arnet.eval()
        self.iunet.eval()

        logger.info("All models loaded successfully")

    def detect_faces(self, image):
        """
        Detect faces using YOLO-FaceV2

        Args:
            image: Input image as numpy array (BGR)

        Returns:
            face_box: Largest face bounding box [x1, y1, x2, y2] or None
        """
        img_height, img_width = image.shape[:2]

        # Prepare image for YOLO
        img = cv2.resize(image, (640, 640))
        img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, HWC to CHW
        img = np.ascontiguousarray(img)
        img = torch.from_numpy(img).to(self.device).float() / 255.0

        if img.ndimension() == 3:
            img = img.unsqueeze(0)

        # Run inference
        with torch.no_grad():
            pred = self.yolo_model(img)[0]
            pred = non_max_suppression(pred, conf_thres=0.25, iou_thres=0.45)

        # Process detections
        if pred[0] is not None and len(pred[0]):
            det = pred[0]
            # Scale coordinates back to original image size
            det[:, [0, 2]] *= img_width / 640
            det[:, [1, 3]] *= img_height / 640

            # Find largest face by area
            areas = (det[:, 2] - det[:, 0]) * (det[:, 3] - det[:, 1])
            largest_idx = torch.argmax(areas)
            face_box = det[largest_idx, :4].cpu().numpy().astype(int)

            logger.info(f"Detected face: {face_box}")
            return face_box

        logger.warning("No faces detected")
        return None

    def segment_portrait(self, image):
        """
        Segment portrait using a simple approach
        Note: This is a placeholder - you would need to implement PaddleSeg integration

        Args:
            image: Input image as numpy array (BGR)

        Returns:
            mask: Portrait mask as numpy array
        """
        # Placeholder implementation - create a simple center-focused mask
        # In a real implementation, you would use PaddleSeg here
        h, w = image.shape[:2]
        mask = np.zeros((h, w), dtype=np.uint8)

        # Create elliptical mask in center (rough portrait approximation)
        center_x, center_y = w // 2, h // 2
        cv2.ellipse(mask, (center_x, center_y), (w//3, h//2), 0, 0, 360, 255, -1)

        logger.info("Portrait segmentation completed (placeholder)")
        return mask

    def estimate_depth(self, image):
        """
        Estimate depth using Depth-Anything-V2

        Args:
            image: Input image as numpy array (BGR)

        Returns:
            depth: Raw depth map as numpy array (not normalized)
        """
        with torch.no_grad():
            depth = self.depth_model.infer_image(image, input_size=518)

        logger.info("Depth estimation completed")
        return depth

    def process_depth_map(self, depth_map):
        """
        Process depth map with simplified logic

        Args:
            depth_map: Raw depth map from depth estimation

        Returns:
            processed_depth: Processed depth map
        """
        if self.bokeh_config['normalize_depth']:
            # Min-max normalization to 0-1 range
            depth_min = depth_map.min()
            depth_max = depth_map.max()

            if depth_max - depth_min > 1e-6:
                processed_depth = (depth_map - depth_min) / (depth_max - depth_min)
            else:
                processed_depth = np.ones_like(depth_map) * 0.5

            logger.info(f"Normalized depth: [{depth_min:.3f}, {depth_max:.3f}] -> [0, 1]")
        else:
            # Keep original depth range without normalization
            processed_depth = depth_map.copy()
            logger.info(f"Preserved original depth range: [{depth_map.min():.3f}, {depth_map.max():.3f}]")

        return processed_depth

    def calculate_focus_depth(self, depth_map, face_box=None, portrait_mask=None):
        """
        Calculate focus depth from face region or portrait region

        Args:
            depth_map: Processed depth map (0-1 range if normalized, original range if not)
            face_box: Face bounding box [x1, y1, x2, y2] or None
            portrait_mask: Portrait mask or None

        Returns:
            focus_depth: Average depth value for focusing
        """
        if not self.bokeh_config['auto_focus']:
            focus_depth = self.bokeh_config['manual_focus_depth']
            logger.info(f"Using manual focus depth: {focus_depth}")
            return focus_depth + self.bokeh_config['focus_offset']

        if face_box is not None:
            x1, y1, x2, y2 = face_box
            # Expand face box slightly for better depth estimation
            h, w = depth_map.shape
            expand_ratio = 0.1
            face_w, face_h = x2 - x1, y2 - y1
            x1 = max(0, int(x1 - face_w * expand_ratio))
            y1 = max(0, int(y1 - face_h * expand_ratio))
            x2 = min(w, int(x2 + face_w * expand_ratio))
            y2 = min(h, int(y2 + face_h * expand_ratio))

            face_depth = np.mean(depth_map[y1:y2, x1:x2])
            focus_depth = face_depth + self.bokeh_config['focus_offset']
            logger.info(f"Using expanded face depth: {face_depth:.3f} + offset {self.bokeh_config['focus_offset']:.3f} = {focus_depth:.3f}")
            return focus_depth
        elif portrait_mask is not None:
            portrait_depth = np.mean(depth_map[portrait_mask > 0])
            focus_depth = portrait_depth + self.bokeh_config['focus_offset']
            logger.info(f"Using portrait depth: {portrait_depth:.3f} + offset {self.bokeh_config['focus_offset']:.3f} = {focus_depth:.3f}")
            return focus_depth
        else:
            # Default to center region
            h, w = depth_map.shape
            center_depth = np.mean(depth_map[h//4:3*h//4, w//4:3*w//4])
            focus_depth = center_depth + self.bokeh_config['focus_offset']
            logger.info(f"Using center depth: {center_depth:.3f} + offset {self.bokeh_config['focus_offset']:.3f} = {focus_depth:.3f}")
            return focus_depth

    def apply_bokeh_effect(self, image, depth_map, focus_depth):
        """
        Apply bokeh effect using BokehMe with simplified configuration

        Args:
            image: Input image as numpy array (BGR)
            depth_map: Processed depth map (0-1 range if normalized, original range if not)
            focus_depth: Focus depth value

        Returns:
            bokeh_image: Image with bokeh effect applied
        """
        # Convert image to RGB and normalize
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0

        # Apply highlight enhancement if enabled
        if self.bokeh_config['highlight']:
            logger.info("Applying highlight enhancement")
            # Out-of-focus areas mask
            mask1 = np.clip(np.tanh(200 * (np.abs(depth_map - focus_depth)**2 - 0.01)), 0, 1)[..., np.newaxis]
            # Highlight areas mask
            mask2 = np.clip(np.tanh(10*(image_rgb - self.bokeh_config['highlight_RGB_threshold'])), 0, 1)
            mask = mask1 * mask2
            image_rgb = image_rgb * (1 + mask * self.bokeh_config['highlight_enhance_ratio'])

        # Calculate defocus map using configuration parameters
        K = self.bokeh_config['K']
        defocus_scale = self.bokeh_config['defocus_scale']
        defocus = K * (depth_map - focus_depth) / defocus_scale

        logger.info(f"Bokeh parameters: K={K}, focus_depth={focus_depth:.3f}, defocus_scale={defocus_scale}")
        logger.info(f"Defocus range: [{defocus.min():.3f}, {defocus.max():.3f}]")

        # Convert to tensors
        image_tensor = torch.from_numpy(image_rgb).permute(2, 0, 1).unsqueeze(0).to(self.device)
        defocus_tensor = torch.from_numpy(defocus).unsqueeze(0).unsqueeze(0).to(self.device)

        with torch.no_grad():
            # Apply bokeh pipeline
            gamma = self.bokeh_config['gamma']
            bokeh_result = self._bokeh_pipeline(image_tensor, defocus_tensor, gamma)

            # Convert back to numpy
            bokeh_image = bokeh_result[0].cpu().permute(1, 2, 0).numpy()
            bokeh_image = np.clip(bokeh_image * 255, 0, 255).astype(np.uint8)
            bokeh_image = cv2.cvtColor(bokeh_image, cv2.COLOR_RGB2BGR)

        logger.info("Bokeh effect applied successfully")
        return bokeh_image

    def _classical_only_pipeline(self, image, defocus, gamma):
        """Classical rendering only (no neural network)"""
        try:
            # Apply classical rendering
            bokeh_classical, _ = self.classical_renderer(image**gamma, defocus*self.bokeh_config['defocus_scale'])
            bokeh_classical = bokeh_classical ** (1/gamma)
            return bokeh_classical.clamp(0, 1)
        except Exception as e:
            logger.error(f"Classical rendering failed: {str(e)}")
            # Return original image as last resort
            return image

    def _gaussian_blur(self, x, r, sigma=None):
        """Gaussian blur function matching BokehMe implementation"""
        r = int(round(r))
        if sigma is None:
            sigma = 0.3 * (r - 1) + 0.8

        # Create meshgrid for kernel
        x_grid, y_grid = torch.meshgrid(
            torch.arange(-int(r), int(r) + 1, device=x.device),
            torch.arange(-int(r), int(r) + 1, device=x.device),
            indexing='ij'
        )

        # Create Gaussian kernel
        kernel = torch.exp(-(x_grid ** 2 + y_grid ** 2) / 2 / sigma ** 2)
        kernel = kernel.float() / kernel.sum()
        kernel = kernel.expand(1, 1, 2*r+1, 2*r+1)

        # Apply convolution with padding
        x = F.pad(x, pad=(r, r, r, r), mode='replicate')
        x = F.conv2d(x, weight=kernel, padding=0)
        return x

    def _bokeh_pipeline(self, image, defocus, gamma):
        """Internal bokeh processing pipeline matching BokehMe demo.py exactly"""
        # Step 1: Classical rendering
        bokeh_classical, defocus_dilate = self.classical_renderer(image**gamma, defocus*self.bokeh_config['defocus_scale'])
        bokeh_classical = bokeh_classical ** (1/gamma)
        defocus_dilate = defocus_dilate / self.bokeh_config['defocus_scale']

        # Step 2: Normalize gamma
        gamma_norm = (gamma - self.bokeh_config['gamma_min']) / (self.bokeh_config['gamma_max'] - self.bokeh_config['gamma_min'])
        gamma_norm = torch.clamp(torch.tensor(gamma_norm, device=image.device), 0, 1).item()

        # Step 3: Calculate adaptive scale
        adapt_scale = max(defocus.abs().max().item(), 1)

        # Step 4: Initial neural network processing at reduced scale
        image_re = F.interpolate(image, scale_factor=1/adapt_scale, mode='bilinear', align_corners=True)
        defocus_re = (1 / adapt_scale) * F.interpolate(defocus, scale_factor=1/adapt_scale, mode='bilinear', align_corners=True)

        bokeh_neural, error_map = self.arnet(image_re, defocus_re, gamma_norm)
        error_map = F.interpolate(error_map, size=(image.shape[2], image.shape[3]), mode='bilinear', align_corners=True)
        bokeh_neural.clamp_(0, 1e5)

        # Step 5: Multi-scale refinement using IUNet
        scale = -1
        for scale in range(int(np.log2(adapt_scale))):
            ratio = 2**(scale+1) / adapt_scale
            h_re, w_re = int(ratio * image.shape[2]), int(ratio * image.shape[3])

            # Ensure minimum size
            h_re = max(h_re, 32)
            w_re = max(w_re, 32)

            # Resize inputs for this scale
            image_re = F.interpolate(image, size=(h_re, w_re), mode='bilinear', align_corners=True)
            defocus_re = ratio * F.interpolate(defocus, size=(h_re, w_re), mode='bilinear', align_corners=True)
            defocus_dilate_re = ratio * F.interpolate(defocus_dilate, size=(h_re, w_re), mode='bilinear', align_corners=True)

            # Refine bokeh_neural using IUNet
            bokeh_neural_refine = self.iunet(image_re, defocus_re.clamp(-1, 1), bokeh_neural, gamma_norm).clamp(0, 1e5)

            # Create mask for smooth blending
            mask = self._gaussian_blur(
                ((defocus_dilate_re < 1) * (defocus_dilate_re > -1)).float(),
                0.005 * (defocus_dilate_re.shape[2] + defocus_dilate_re.shape[3])
            )

            # Blend refined and interpolated results
            bokeh_neural = (mask * bokeh_neural_refine +
                            (1 - mask) * F.interpolate(bokeh_neural, size=(h_re, w_re), mode='bilinear', align_corners=True))

        # Step 6: Final refinement at full resolution
        bokeh_neural_refine = self.iunet(image, defocus.clamp(-1, 1), bokeh_neural, gamma_norm).clamp(0, 1e5)
        mask = self._gaussian_blur(
            ((defocus_dilate < 1) * (defocus_dilate > -1)).float(),
            0.005 * (defocus_dilate.shape[2] + defocus_dilate.shape[3])
        )
        bokeh_neural = (mask * bokeh_neural_refine +
                        (1 - mask) * F.interpolate(bokeh_neural, size=(image.shape[2], image.shape[3]), mode='bilinear', align_corners=True))

        # Step 7: Combine classical and neural results
        bokeh_pred = bokeh_classical * (1 - error_map) + bokeh_neural * error_map

        logger.info(f"Bokeh pipeline completed - adapt_scale: {adapt_scale:.2f}, scales processed: {scale+1}")
        return bokeh_pred.clamp(0, 1)

    def save_intermediate_outputs(self, image, face_box, portrait_mask, depth_map, output_dir, image_name):
        """
        Save intermediate processing results as image files

        Args:
            image: Original input image
            face_box: Face bounding box [x1, y1, x2, y2] or None
            portrait_mask: Portrait segmentation mask
            depth_map: Normalized depth map
            output_dir: Directory to save intermediate files
            image_name: Base name for output files
        """
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.splitext(image_name)[0]

        # 1. Save face detection visualization
        if face_box is not None:
            face_vis = image.copy()
            x1, y1, x2, y2 = face_box
            cv2.rectangle(face_vis, (x1, y1), (x2, y2), (0, 255, 0), 3)
            cv2.putText(face_vis, "Face", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            face_output_path = os.path.join(output_dir, f"{base_name}_face_detection.jpg")
            cv2.imwrite(face_output_path, face_vis)
            logger.info(f"Face detection saved: {face_output_path}")

        # 2. Save portrait mask
        mask_output_path = os.path.join(output_dir, f"{base_name}_portrait_mask.jpg")
        cv2.imwrite(mask_output_path, portrait_mask)
        logger.info(f"Portrait mask saved: {mask_output_path}")

        # 3. Save portrait mask overlay on original image
        mask_overlay = image.copy()
        mask_overlay[portrait_mask > 0] = mask_overlay[portrait_mask > 0] * 0.7 + np.array([0, 255, 0]) * 0.3
        mask_overlay_path = os.path.join(output_dir, f"{base_name}_portrait_overlay.jpg")
        cv2.imwrite(mask_overlay_path, mask_overlay.astype(np.uint8))
        logger.info(f"Portrait mask overlay saved: {mask_overlay_path}")

        # 4. Save depth map (grayscale)
        depth_gray = (depth_map * 255).astype(np.uint8)
        depth_output_path = os.path.join(output_dir, f"{base_name}_depth_map.jpg")
        cv2.imwrite(depth_output_path, depth_gray)
        logger.info(f"Depth map saved: {depth_output_path}")

        # 5. Save depth map with color visualization
        depth_colored = cv2.applyColorMap(depth_gray, cv2.COLORMAP_INFERNO)
        depth_colored_path = os.path.join(output_dir, f"{base_name}_depth_colored.jpg")
        cv2.imwrite(depth_colored_path, depth_colored)
        logger.info(f"Colored depth map saved: {depth_colored_path}")

        # 6. Save combined visualization (original + face + mask + depth)
        h, w = image.shape[:2]

        # Resize all images to same size for combination
        img_resized = cv2.resize(image, (w//2, h//2))

        if face_box is not None:
            face_vis_resized = cv2.resize(face_vis, (w//2, h//2))
        else:
            face_vis_resized = cv2.resize(image, (w//2, h//2))

        mask_overlay_resized = cv2.resize(mask_overlay.astype(np.uint8), (w//2, h//2))
        depth_colored_resized = cv2.resize(depth_colored, (w//2, h//2))

        # Create 2x2 grid
        top_row = np.hstack([img_resized, face_vis_resized])
        bottom_row = np.hstack([mask_overlay_resized, depth_colored_resized])
        combined = np.vstack([top_row, bottom_row])

        # Add labels
        cv2.putText(combined, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(combined, "Face Detection", (w//2 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(combined, "Portrait Mask", (10, h//2 + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(combined, "Depth Map", (w//2 + 10, h//2 + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        combined_path = os.path.join(output_dir, f"{base_name}_combined_visualization.jpg")
        cv2.imwrite(combined_path, combined)
        logger.info(f"Combined visualization saved: {combined_path}")

    def process_image(self, image_path, output_path=None, save_intermediates=True, intermediate_dir=None):
        """
        Process a single image through the complete pipeline

        Args:
            image_path: Path to input image
            output_path: Path to save output image (optional)
            save_intermediates: Whether to save intermediate results
            intermediate_dir: Directory to save intermediate files (default: same as output)
            tag: Optional tag for image (e.g., filename)

        Returns:
            result: Dictionary with processing results
        """
        logger.info(f"Processing image: {image_path}")
        start_time = time.time()

        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")

        # Step 1: Face detection
        face_box = self.detect_faces(image)

        # Step 2: Portrait segmentation
        portrait_mask = self.segment_portrait(image)

        # Step 3: Depth estimation
        raw_depth_map = self.estimate_depth(image)

        # Step 4: Process depth map according to configuration
        processed_depth_map = self.process_depth_map(raw_depth_map)

        # Step 5: Calculate focus depth
        focus_depth = self.calculate_focus_depth(processed_depth_map, face_box, portrait_mask)

        # Step 6: Apply bokeh effect
        bokeh_image = self.apply_bokeh_effect(image, processed_depth_map, focus_depth)

        # Save intermediate outputs if requested
        if save_intermediates:
            if intermediate_dir is None:
                if output_path:
                    intermediate_dir = os.path.join(os.path.dirname(output_path), "intermediates")
                else:
                    intermediate_dir = os.path.join(os.path.dirname(image_path), "intermediates")

            image_name = os.path.basename(image_path)
            self.save_intermediate_outputs(image, face_box, portrait_mask, processed_depth_map,
                                         intermediate_dir, image_name)

        # Save final result if output path provided
        if output_path:
            origin_copy_path = output_path.replace('_bokeh.jpg', '.jpg').replace('_bokeh.png', '.png')
            cv2.imwrite(origin_copy_path, image)
            cv2.imwrite(output_path, bokeh_image)
            logger.info(f"Final result saved to: {output_path}")

        processing_time = time.time() - start_time
        logger.info(f"Processing completed in {processing_time:.2f} seconds")

        return {
            'success': True,
            'face_box': face_box,
            'portrait_mask': portrait_mask,
            'raw_depth_map': raw_depth_map,
            'processed_depth_map': processed_depth_map,
            'focus_depth': focus_depth,
            'bokeh_image': bokeh_image,
            'processing_time': processing_time,
            'intermediate_dir': intermediate_dir if save_intermediates else None
        }

def main():
    """Main function for testing the pipeline"""
    import argparse

    # 设置输入和输出路径
    INPUT_DIR = "/home/<USER>/T2/data/bokeh_test"
    OUTPUT_ROOT = "/home/<USER>/T2/data/pipeline_output"

    parser = argparse.ArgumentParser(description='Simplified Portrait Pipeline')
    parser.add_argument('--input', type=str, default=INPUT_DIR, help='Input image path or directory')
    parser.add_argument('--output', type=str, default=OUTPUT_ROOT, help='Output directory')
    parser.add_argument('--no-intermediates', action='store_true',
                       help='Skip saving intermediate results')
    parser.add_argument('--intermediate-dir', type=str,
                       help='Directory to save intermediate files (default: output_dir/intermediates)')

    # Bokeh effect parameters
    parser.add_argument('--blur-strength', type=float, default=10.0,
                       help='Blur strength parameter K (default: 10.0)')
    parser.add_argument('--gamma', type=float, default=4.0,
                       help='Gamma value for processing (1.0-5.0, default: 4.0)')
    parser.add_argument('--defocus-scale', type=float, default=10.0,
                       help='Defocus scale parameter (default: 10.0)')

    # Depth processing parameters (simplified)
    parser.add_argument('--no-normalize-depth', action='store_true',
                       help='Skip depth normalization (preserve original range)')

    # Focus control parameters
    parser.add_argument('--manual-focus', type=float, metavar='DEPTH',
                       help='Manual focus depth (0-1), disables auto focus')
    parser.add_argument('--focus-offset', type=float, default=0.0,
                       help='Offset to add to calculated focus depth (default: 0.0)')

    # Highlight enhancement
    parser.add_argument('--highlight', action='store_true',
                       help='Enable highlight enhancement')
    parser.add_argument('--highlight-threshold', type=float, default=220/255,
                       help='RGB threshold for highlights (default: 0.863)')
    parser.add_argument('--highlight-ratio', type=float, default=0.4,
                       help='Enhancement ratio for highlights (default: 0.4)')
    
    # tag
    parser.add_argument('--tag', type=str, default='',
                       help='Optional tag for image (e.g., filename)')

    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output, exist_ok=True)

    # Create bokeh configuration from command line arguments (simplified)
    bokeh_config = {
        'K': args.blur_strength,
        'gamma': args.gamma,
        'defocus_scale': args.defocus_scale,
        'normalize_depth': not args.no_normalize_depth,
        'auto_focus': args.manual_focus is None,
        'manual_focus_depth': args.manual_focus if args.manual_focus is not None else 0.5,
        'focus_offset': args.focus_offset,
        'highlight': args.highlight,
        'highlight_RGB_threshold': args.highlight_threshold,
        'highlight_enhance_ratio': args.highlight_ratio,
    }

    # Initialize pipeline with configuration
    pipeline = SimplifiedPortraitPipeline(bokeh_config=bokeh_config)

    # Print configuration
    logger.info("Bokeh Configuration:")
    for key, value in bokeh_config.items():
        logger.info(f"  {key}: {value}")

    # Determine if input is a single file or directory
    input_path = Path(args.input)

    if input_path.is_file():
        # Process single image
        file_paths = [input_path]
    elif input_path.is_dir():
        # Process all images in directory
        file_paths = list(input_path.glob('**/*'))
        file_paths = [f for f in file_paths if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']]
    else:
        logger.error(f"Input path does not exist: {args.input}")
        return

    logger.info(f"Found {len(file_paths)} images to process")

    # Process each image
    for i, file_path in enumerate(file_paths):
        try:
            logger.info(f"[{i+1}/{len(file_paths)}] Processing: {file_path.name}")

            output_path = os.path.join(args.output, file_path.name.replace('.jpg', f'_bokeh.jpg').replace('.png', '_bokeh.png'))

            result = pipeline.process_image(
                str(file_path),
                output_path,
                save_intermediates=not args.no_intermediates,
                intermediate_dir=args.intermediate_dir
            )

            print(f"✓ Processed {file_path.name} -> {output_path}")
            print(f"  Time: {result['processing_time']:.2f}s")

            if result['face_box'] is not None:
                print(f"  Face detected: {result['face_box']}")
            else:
                print("  No face detected")

            print(f"  Focus depth: {result['focus_depth']:.3f}")

            if result['intermediate_dir']:
                print(f"  Intermediates: {result['intermediate_dir']}")

        except Exception as e:
            logger.error(f"Failed to process {file_path.name}: {str(e)}")
            continue

    print(f"\nCompleted processing {len(file_paths)} images")

if __name__ == "__main__":
    main()
