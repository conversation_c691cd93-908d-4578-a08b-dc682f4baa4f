_base_: '../_base_/pascal_voc12aug.yml'

model:
  type: DNLNet
  backbone:
    type: ResNet101_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet101_vd_ssld.tar.gz

optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 4.0e-05

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  power: 0.9

loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
  coef: [1, 0.4]
