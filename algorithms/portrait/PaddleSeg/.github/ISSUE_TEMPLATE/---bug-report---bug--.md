---
name: "\U0001F41B Bug report / Bug反馈"
about: Create a report to help us improve / 反馈Bug问题
title: "[Bug]"
labels: bug
assignees: ''

---

Thanks for your bug report. To help us solve the issue better, please provide following information：
 1. PaddleSeg version: (please specify the branch as well，e.g. PaddleSeg release/2.3)
 2. PaddlePaddle version: (e.g. PaddlePaddle 2.1.0)
 3. Operation system: (e.g. Linux/Windows/MacOS)
 4. Python version: (e.g. Python3.6/7/8)
 5. CUDA/cuDNN version: (e.g. CUDA10.2/cuDNN 7.6.5)
 6. Full codes: (if you modify any original code，please show the comparison of the codes before and after)
 7. Detailed error information, releated running log: (if using multi-gpus，the log is saved to log/worklog.0 by default)
 8. Running command or reproduce details：
 9. Additional context: (Add any other context about the problem)

---

欢迎您反馈PaddleSeg使用问题，辛苦您提供以下信息，方便我们快速定位和解决问题：
 1. PaddleSeg版本：（请提供版本号和分支信息，如PaddleSeg release/2.3）
 2. PaddlePaddle版本：（如PaddlePaddle 2.1.0）
 3. 操作系统信息：（如Linux/Windows/MacOS）
 4. Python版本号：（如Python3.6/7/8）
 5. CUDA/cuDNN版本：（ 如CUDA10.2/cuDNN 7.6.5等）
 6. 完整的代码：(若修改过原代码，请提供修改前后代码对比）
 7. 详细的错误信息、相关log：（若使用多卡，log默认保存在log/worklog.0）
 8. 运行指令或复现步骤：
 9. 其他内容: （增加其他与问题相关的内容）
