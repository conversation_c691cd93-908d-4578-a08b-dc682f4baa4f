_base_: '../_base_/ade20k.yml'
batch_size: 4
iters: 120000

train_dataset:
  transforms:
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0.25
    - type: RandomPaddingCrop
      crop_size: [480, 480]
      im_padding_value: [0, 0, 0]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.4
      contrast_range: 0.4
      saturation_range: 0.4
    - type: Normalize

model:
  type: FastFCN
  backbone:
    type: ResNet50_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet50_vd_ssld_v2.tar.gz
  num_codes: 32
  mid_channels: 512
  use_jpu: True
  aux_loss: True
  use_se_loss: True
  add_lateral: True

loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
    - type: SECrossEntropyLoss
  coef: [1, 0.4, 0.2]

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  end_lr: 0
  power: 0.9
