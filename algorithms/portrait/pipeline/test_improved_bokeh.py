#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for improved bokeh effects with depth control parameters
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_raw import Simplified<PERSON><PERSON>rait<PERSON><PERSON><PERSON><PERSON>

def test_depth_normalization_modes():
    """Test different depth normalization approaches"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/test_image.jpg"
    output_dir = "/home/<USER>/T2/data/depth_normalization_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Test configurations
    configs = [
        {
            'name': 'simple_normalization',
            'config': {
                'normalize_depth': True,
                'preserve_depth_range': False,
                'depth_min_percentile': 0,
                'depth_max_percentile': 100,
                'K': 60.0,
                'gamma': 4.0
            }
        },
        {
            'name': 'robust_normalization',
            'config': {
                'normalize_depth': True,
                'preserve_depth_range': False,
                'depth_min_percentile': 5,
                'depth_max_percentile': 95,
                'K': 60.0,
                'gamma': 4.0
            }
        },
        {
            'name': 'preserve_depth_range',
            'config': {
                'normalize_depth': False,
                'preserve_depth_range': True,
                'depth_scale_factor': 0.001,  # Scale down for better effect
                'K': 60.0,
                'gamma': 4.0
            }
        },
        {
            'name': 'scaled_depth',
            'config': {
                'normalize_depth': True,
                'preserve_depth_range': False,
                'depth_min_percentile': 10,
                'depth_max_percentile': 90,
                'depth_scale_factor': 1.5,  # Enhance depth effect
                'K': 60.0,
                'gamma': 4.0
            }
        }
    ]
    
    print("="*60)
    print("Testing Depth Normalization Modes")
    print("="*60)
    
    for test_config in configs:
        print(f"\nTesting: {test_config['name']}")
        print("-" * 40)
        
        # Initialize pipeline with specific config
        pipeline = SimplifiedPortraitPipeline(bokeh_config=test_config['config'])
        
        # Process image
        output_path = os.path.join(output_dir, f"bokeh_{test_config['name']}.jpg")
        intermediate_dir = os.path.join(output_dir, f"intermediates_{test_config['name']}")
        
        result = pipeline.process_image(
            test_image,
            output_path,
            save_intermediates=True,
            intermediate_dir=intermediate_dir
        )
        
        print(f"✓ Output: {output_path}")
        print(f"✓ Time: {result['processing_time']:.2f}s")
        print(f"✓ Focus depth: {result['focus_depth']:.3f}")
        print(f"✓ Raw depth range: [{result['raw_depth_map'].min():.3f}, {result['raw_depth_map'].max():.3f}]")
        print(f"✓ Processed depth range: [{result['processed_depth_map'].min():.3f}, {result['processed_depth_map'].max():.3f}]")

def test_bokeh_parameters():
    """Test different bokeh effect parameters"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/test_image.jpg"
    output_dir = "/home/<USER>/T2/data/bokeh_parameters_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Test different parameter combinations
    parameter_tests = [
        {
            'name': 'weak_blur',
            'config': {'K': 20.0, 'gamma': 2.0, 'defocus_scale': 15.0}
        },
        {
            'name': 'medium_blur',
            'config': {'K': 60.0, 'gamma': 4.0, 'defocus_scale': 10.0}
        },
        {
            'name': 'strong_blur',
            'config': {'K': 100.0, 'gamma': 5.0, 'defocus_scale': 8.0}
        },
        {
            'name': 'artistic_blur',
            'config': {'K': 80.0, 'gamma': 3.5, 'defocus_scale': 12.0, 'highlight': True}
        }
    ]
    
    print("\n" + "="*60)
    print("Testing Bokeh Parameters")
    print("="*60)
    
    for test in parameter_tests:
        print(f"\nTesting: {test['name']}")
        print("-" * 40)
        
        # Initialize pipeline with specific config
        pipeline = SimplifiedPortraitPipeline(bokeh_config=test['config'])
        
        # Process image
        output_path = os.path.join(output_dir, f"bokeh_{test['name']}.jpg")
        
        result = pipeline.process_image(
            test_image,
            output_path,
            save_intermediates=False
        )
        
        print(f"✓ Output: {output_path}")
        print(f"✓ Time: {result['processing_time']:.2f}s")
        print(f"✓ Parameters: {test['config']}")

def test_focus_control():
    """Test different focus control methods"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/test_image.jpg"
    output_dir = "/home/<USER>/T2/data/focus_control_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Test different focus methods
    focus_tests = [
        {
            'name': 'auto_focus',
            'config': {'auto_focus': True, 'focus_offset': 0.0}
        },
        {
            'name': 'auto_focus_offset_near',
            'config': {'auto_focus': True, 'focus_offset': -0.1}
        },
        {
            'name': 'auto_focus_offset_far',
            'config': {'auto_focus': True, 'focus_offset': 0.1}
        },
        {
            'name': 'manual_focus_near',
            'config': {'auto_focus': False, 'manual_focus_depth': 0.3}
        },
        {
            'name': 'manual_focus_center',
            'config': {'auto_focus': False, 'manual_focus_depth': 0.5}
        },
        {
            'name': 'manual_focus_far',
            'config': {'auto_focus': False, 'manual_focus_depth': 0.7}
        }
    ]
    
    print("\n" + "="*60)
    print("Testing Focus Control")
    print("="*60)
    
    for test in focus_tests:
        print(f"\nTesting: {test['name']}")
        print("-" * 40)
        
        # Initialize pipeline with specific config
        pipeline = SimplifiedPortraitPipeline(bokeh_config=test['config'])
        
        # Process image
        output_path = os.path.join(output_dir, f"bokeh_{test['name']}.jpg")
        
        result = pipeline.process_image(
            test_image,
            output_path,
            save_intermediates=False
        )
        
        print(f"✓ Output: {output_path}")
        print(f"✓ Time: {result['processing_time']:.2f}s")
        print(f"✓ Focus depth: {result['focus_depth']:.3f}")
        print(f"✓ Config: {test['config']}")

def test_highlight_enhancement():
    """Test highlight enhancement feature"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/test_image.jpg"
    output_dir = "/home/<USER>/T2/data/highlight_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Test with and without highlight enhancement
    highlight_tests = [
        {
            'name': 'no_highlight',
            'config': {'highlight': False}
        },
        {
            'name': 'highlight_default',
            'config': {'highlight': True, 'highlight_RGB_threshold': 220/255, 'highlight_enhance_ratio': 0.4}
        },
        {
            'name': 'highlight_strong',
            'config': {'highlight': True, 'highlight_RGB_threshold': 200/255, 'highlight_enhance_ratio': 0.6}
        },
        {
            'name': 'highlight_subtle',
            'config': {'highlight': True, 'highlight_RGB_threshold': 240/255, 'highlight_enhance_ratio': 0.2}
        }
    ]
    
    print("\n" + "="*60)
    print("Testing Highlight Enhancement")
    print("="*60)
    
    for test in highlight_tests:
        print(f"\nTesting: {test['name']}")
        print("-" * 40)
        
        # Initialize pipeline with specific config
        pipeline = SimplifiedPortraitPipeline(bokeh_config=test['config'])
        
        # Process image
        output_path = os.path.join(output_dir, f"bokeh_{test['name']}.jpg")
        
        result = pipeline.process_image(
            test_image,
            output_path,
            save_intermediates=False
        )
        
        print(f"✓ Output: {output_path}")
        print(f"✓ Time: {result['processing_time']:.2f}s")
        print(f"✓ Config: {test['config']}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Improved Bokeh Effects')
    parser.add_argument('--test', choices=['depth', 'bokeh', 'focus', 'highlight', 'all'], 
                       default='all', help='Which test to run')
    
    args = parser.parse_args()
    
    if args.test in ['depth', 'all']:
        test_depth_normalization_modes()
    
    if args.test in ['bokeh', 'all']:
        test_bokeh_parameters()
    
    if args.test in ['focus', 'all']:
        test_focus_control()
    
    if args.test in ['highlight', 'all']:
        test_highlight_enhancement()
    
    print("\n" + "="*60)
    print("All tests completed!")
    print("="*60)
