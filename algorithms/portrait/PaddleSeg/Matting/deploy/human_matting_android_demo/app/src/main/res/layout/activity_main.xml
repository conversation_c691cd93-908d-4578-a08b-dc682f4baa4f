<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                             xmlns:app="http://schemas.android.com/apk/res-auto"
                                             xmlns:tools="http://schemas.android.com/tools"
                                             android:layout_width="match_parent"
                                             android:layout_height="match_parent">

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/v_input_info"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_input_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="5dp"
                android:lineSpacingExtra="4dp"
                android:maxLines="6"
                android:scrollbars="vertical"
                android:singleLine="false"
                android:text="" />

        </LinearLayout>

        <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/v_output_info"
                android:layout_below="@+id/v_input_info">

            <ImageView
                    android:id="@+id/iv_input_image"
                    android:layout_width="400dp"
                    android:layout_height="400dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"/>
        </RelativeLayout>


        <RelativeLayout
                android:id="@+id/v_output_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true">

            <TextView
                    android:id="@+id/tv_output_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:scrollbars="vertical"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:textAlignment="center"
                    android:lineSpacingExtra="5dp"
                    android:singleLine="false"
                    android:maxLines="5"
                    android:text=""
                    android:gravity="center_horizontal" />

            <TextView
                    android:id="@+id/tv_inference_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_output_result"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:textAlignment="center"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="10dp"
                    android:text=""
                    android:gravity="center_horizontal" />

            <Button
                android:id="@+id/save_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_inference_time"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:textAlignment="center"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:text="保存图像"
                android:gravity="center"
                android:onClick="clickSaveImg"/>

            <ImageView
                android:id="@+id/paddlelogo"
                android:layout_width="400dp"
                android:layout_height="40dp"
                android:layout_below="@+id/save_img"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:src="@drawable/paddle_logo"/>

        </RelativeLayout>

    </RelativeLayout>

</android.support.constraint.ConstraintLayout>