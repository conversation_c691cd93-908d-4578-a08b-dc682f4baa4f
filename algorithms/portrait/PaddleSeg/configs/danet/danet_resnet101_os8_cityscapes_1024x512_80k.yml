_base_: '../_base_/cityscapes.yml'

batch_size: 1
iters: 80000

model:
  type: DANet
  backbone:
    type: ResNet101_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet101_vd_ssld.tar.gz
  num_classes: 19
  backbone_indices: [2, 3]

optimizer:
  type: sgd

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  power: 0.9

loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
  coef: [1, 1, 1, 0.4]
