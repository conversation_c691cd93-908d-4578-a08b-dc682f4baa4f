Channels:
 - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch
 - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
 - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
 - defaults
 - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
Platform: linux-64
Collecting package metadata (repodata.json): ...working... done
Solving environment: ...working... done

## Package Plan ##

  environment location: /home/<USER>/miniconda3/envs/bokeh-paddleseg-yolov2-depthanyv2

  added / updated specs:
    - seaborn


The following packages will be downloaded:

    package                    |            build
    ---------------------------|-----------------
    seaborn-0.13.2             |   py38h06a4308_0         604 KB  https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
    ------------------------------------------------------------
                                           Total:         604 KB

The following NEW packages will be INSTALLED:

  seaborn            anaconda/pkgs/main/linux-64::seaborn-0.13.2-py38h06a4308_0 


Proceed ([y]/n)? 

Downloading and Extracting Packages: ...working... done
Preparing transaction: ...working... done
Verifying transaction: ...working... done
Executing transaction: ...working... done
