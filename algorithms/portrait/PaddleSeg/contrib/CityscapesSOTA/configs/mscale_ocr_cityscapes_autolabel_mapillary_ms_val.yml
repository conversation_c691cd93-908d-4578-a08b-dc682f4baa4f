batch_size: 1
iters: 65000

model:
  type: MscaleOCRNet
  pretrained: pretrain/pretrained.pdparams
  n_scales: [0.5,1.0,1.5,2.0,2.5] # more scales
  backbone:
    type: HRNet_W48_NV
  num_classes: 19
  backbone_indices: [0]

val_dataset:
  type: CityscapesAutolabeling
  dataset_root: data/cityscapes
  transforms:
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: val

train_dataset:
  type: CityscapesAutolabeling
  dataset_root: data/cityscapes/
  transforms:
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0
    - type: RandomPaddingCrop
      crop_size: [2048, 1024]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.25
      brightness_prob: 1
      contrast_range: 0.25
      contrast_prob: 1
      saturation_range: 0.25
      saturation_prob: 1
      hue_range: 63
      hue_prob: 1
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: train
