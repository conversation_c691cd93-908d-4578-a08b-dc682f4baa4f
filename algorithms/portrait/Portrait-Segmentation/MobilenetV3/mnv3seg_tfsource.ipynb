{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "mnv3seg_tfsource.ipynb", "provenance": [], "collapsed_sections": [], "machine_shape": "hm"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "UlAnexM4kQJ-"}, "source": ["**1. Setup GPU and G-Drive**"]}, {"cell_type": "markdown", "metadata": {"id": "PH7ofbDnLA86"}, "source": ["Choose a **GPU** runtime and check the device."]}, {"cell_type": "code", "metadata": {"id": "5L__CaBt_U-B"}, "source": ["!nvidia-smi"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "WirYBEmaMq09"}, "source": ["Mount your **google drive**."]}, {"cell_type": "code", "metadata": {"id": "Fu1RDG2kMpik"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "eWegl-7zuNBl"}, "source": ["**2. Build Tensorflow From Source**"]}, {"cell_type": "markdown", "metadata": {"id": "Go_3NQl0F2Sz"}, "source": ["Install bazel to build tensorflow from source."]}, {"cell_type": "code", "metadata": {"id": "USWQL5sGuR8j"}, "source": ["!sudo apt install curl gnupg\n", "!curl -fsSL https://bazel.build/bazel-release.pub.gpg | gpg --dearmor > bazel.gpg\n", "!sudo mv bazel.gpg /etc/apt/trusted.gpg.d/\n", "!echo \"deb [arch=amd64] https://storage.googleapis.com/bazel-apt stable jdk1.8\" | sudo tee /etc/apt/sources.list.d/bazel.list\n", "!sudo apt update && sudo apt install bazel\n", "!sudo apt update && sudo apt install bazel-3.1.0"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "nhkQhQn8GHII"}, "source": ["Install the tensorflow pip package **dependencies**."]}, {"cell_type": "code", "metadata": {"id": "hnbrDEF4unwE"}, "source": ["!sudo apt install python3-dev python3-pip\n", "!pip install -U --user pip numpy wheel\n", "!pip install -U --user keras_preprocessing --no-deps"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "X4xh7wxpHJnP"}, "source": ["**Note:** Restart the runtime in order to use newly installed versions."]}, {"cell_type": "markdown", "metadata": {"id": "mGZWRC_8GO1w"}, "source": ["Download the latest tensorflow **source** code"]}, {"cell_type": "code", "metadata": {"id": "qOUELsjyvSjV"}, "source": ["!git clone https://github.com/tensorflow/tensorflow.git\n", "%cd tensorflow"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "tCAWFrHeGUon"}, "source": ["Configure your system build by running the ***./configure*** at the root of your TensorFlow source tree."]}, {"cell_type": "code", "metadata": {"id": "RQgzaL34vihi"}, "source": ["!./configure"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "05UOtb6AGWPi"}, "source": ["**Note:** For GPU support, set cuda=Y and specify the CUDA compute capabilities you want to build with, during configuration."]}, {"cell_type": "markdown", "metadata": {"id": "j1nJKJ2eIxJk"}, "source": ["Build and **install** the package from the master branch."]}, {"cell_type": "code", "metadata": {"id": "yMZ-6M6xwMU0"}, "source": ["!bazel build --config=cuda //tensorflow/tools/pip_package:build_pip_package\n", "!./bazel-bin/tensorflow/tools/pip_package/build_pip_package --nightly_flag /tmp/tensorflow_pkg\n", "!pip install /tmp/tensorflow_pkg/tf_nightly-2.5.0-cp36-cp36m-linux_x86_64.whl"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "2rpUZmQjLwup"}, "source": ["**Note:** The build process can take **several hours** depending on the build and hardware configurations."]}, {"cell_type": "markdown", "metadata": {"id": "VOdoSLCaNUsw"}, "source": ["Once the build is complete, you can save the '**.whl**' file in your **google drive** and install the it directly from your drive, at a later time without having to rebuild the same."]}, {"cell_type": "code", "metadata": {"id": "yb6uPHUuixz8"}, "source": ["!cp /tmp/tensorflow_pkg/tf_nightly-2.5.0-cp36-cp36m-linux_x86_64.whl /content/drive/MyDrive/tf_nigltly_wheel/tf_nightly-2.5.0-cp36-cp36m-linux_x86_64.whl\n", "!pip install /content/drive/MyDrive/tf_nigltly_wheel/tf_nightly-2.5.0-cp36-cp36m-linux_x86_64.whl"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "bHn2k31NB7VX"}, "source": ["**Note:** Here we need to install the latest **nigtly version** since the the **mobilenetv3** architecture is only available in this version."]}, {"cell_type": "markdown", "metadata": {"id": "aPaqrrTtNvSE"}, "source": ["Restart the kernel and check the tensorflow version. "]}, {"cell_type": "code", "metadata": {"id": "VXvXUP8xeEih"}, "source": ["import tensorflow as tf\n", "tf.__version__"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "1Swu0Yhy8lbz"}, "source": ["**3. Prepare Dataset**"]}, {"cell_type": "markdown", "metadata": {"id": "yxR8NdhbEcGh"}, "source": ["Load and extract the **aisegment** dataset images into local drive."]}, {"cell_type": "code", "metadata": {"id": "r7BnL0zLvg7O"}, "source": ["!cp /content/drive/MyDrive/AISEGDS/AISEGDS.zip  .\n", "!unzip AISEGDS.zip"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "YmZnqybY_XI4"}, "source": ["**4. <PERSON><PERSON><PERSON>**"]}, {"cell_type": "code", "metadata": {"id": "_IJh1r5fX9Cw"}, "source": ["from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from tensorflow.keras.callbacks import TensorBoard, ModelCheckpoint, Callback, ReduceLROnPlateau\n", "from tensorflow.keras.layers import *\n", "from tensorflow.keras.models import *\n", "from tensorflow.keras.utils import plot_model\n", "from tensorflow.keras.optimizers import SGD, <PERSON>\n", "from tensorflow.keras.regularizers import l2\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "import os, cv2, imageio\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image, ImageEnhance\n", "from IPython.display import clear_output\n", "from sklearn.utils import shuffle\n", "from sklearn.model_selection import train_test_split"], "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "VMu8HbYKE1Nd"}, "source": ["**5. Model Architecture**"]}, {"cell_type": "markdown", "metadata": {"id": "W3hoP10tubTG"}, "source": ["In the **decoder** module we use **transition blocks** along with upsampling layers , similar to the decode modules in the portrait-net architecture. There are two branches in this block: one branch contains two **depthwise separable convolutions** and  the other contains a single **1×1 convolution** to adjust the number of channels."]}, {"cell_type": "code", "metadata": {"id": "kuINr5yzRTHw"}, "source": ["def bottleneck(x, nfilters):\n", "\n", "  \n", "  y = DepthwiseConv2D(kernel_size=3, depth_multiplier=1, padding='same')(x)\n", "  y = BatchNormalization()(y)\n", "  y = Activation(\"relu\")(y)\n", " \n", "  y = Conv2D(kernel_size=1, filters=nfilters, padding ='same')(y)\n", "  y = BatchNormalization()(y)\n", "  y = Activation(\"relu\")(y)\n", "\n", "\n", "  y = DepthwiseConv2D(kernel_size=3, depth_multiplier=1, padding='same')(y)\n", "  y = BatchNormalization()(y)\n", "  y = Activation(\"relu\")(y)\n", "\n", "  y = Conv2D(kernel_size=1, filters=nfilters, padding ='same')(y)\n", "  y = BatchNormalization()(y)\n", "  \n", "  z = Conv2D(kernel_size=1, filters=nfilters, padding ='same')(x)\n", "  z = BatchNormalization()(z)\n", "\n", "  z = Add()([y, z])\n", "  z = Activation(\"relu\")(z)\n", "\n", "  return z"], "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "TVyYlx8D-wTW"}, "source": ["Here we use minimalistic version of **mobilnetev3** as the encoder part of the network. Use the [mobilenet_v3.py ](https://github.com/tensorflow/tensorflow/blob/master/tensorflow/python/keras/applications/mobilenet_v3.py)file to load the architecture and weights for the pretrained model. For the decoder part, we use an upsampling block with **bilinear resize** along with the aforementioned transition blocks. In case of **skip connections** between encoder anmd decoder, we use **element-wise addition** instead of concatenation for faster inference speed."]}, {"cell_type": "code", "metadata": {"id": "rV-KxeI1Z145"}, "source": ["PRETRAINED='/content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-10-0.03.hdf5'#'/content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-03-0.14.hdf5'\n", "\n", "#  Model architecture: mnv3_unet\n", "\n", "from mobilenet_v3 import * # Dont forget to remove rescale layer\n", "\n", "def mnv3_unet(finetune=False, pretrained=False, alpha=1):\n", "\n", "    # Load pretrained model (if any)\n", "    if (pretrained):\n", "       model=load_model(PRETRAINED)\n", "       for layer in model.layers:\n", "        layer.trainable = True  # Unfreeze all layers\n", "       print(\"Loaded pretrained model ...\\n\")\n", "       return model\n", "\n", "    # Encoder\n", "    mnv3 = MobileNetV3Small(\n", "    input_shape=(256,256,3), alpha=1.0, minimalistic=True, include_top=False,\n", "    weights='imagenet')\n", "\n", "    if (finetune):\n", "      for layer in mnv3.layers[:-3]:\n", "        layer.trainable = False\n", "          \n", "    # Decoder\n", "    x = mnv3.layers[-4].output\n", "\n", "    x = bottleneck(x, 288)\n", "    #x = Conv2DTranspose(filters=288, kernel_size=3, strides=2, padding = 'same', use_bias=False)(x)\n", "    x = UpSampling2D( size=(2, 2), interpolation='bilinear')(x)\n", "    x = Add()([x, mnv3.layers[74].output])#concatenate([x, mnv3.layers[71].output], axis = 3) # 75 l\n", "    \n", "    x = bottleneck(x, 96)\n", "    #x = Conv2DTranspose(filters=96, kernel_size=3, strides=2, padding = 'same', use_bias=False)(x)\n", "    x = UpSampling2D( size=(2, 2), interpolation='bilinear')(x)\n", "    x = Add()([x, mnv3.layers[30].output]) # 32\n", "                \n", "    x = bottleneck(x, 72)\n", "    #x = Conv2DTranspose(filters=72, kernel_size=3, strides=2, padding = 'same', use_bias=False)(x)\n", "    x = UpSampling2D( size=(2, 2), interpolation='bilinear')(x)\n", "    x = Add()([x, mnv3.layers[12].output]) # 13\n", "    \n", "    x = bottleneck(x, 16)\n", "    #x = Conv2DTranspose(filters=72, kernel_size=3, strides=2, padding = 'same', use_bias=False)(x)\n", "    x = UpSampling2D( size=(2, 2), interpolation='bilinear')(x)\n", "                \n", "    #x = Conv2DTranspose(filters=8, kernel_size=3, strides=2, padding='same', use_bias=False)(x)\n", "    x = UpSampling2D( size=(2, 2), interpolation='bilinear')(x)\n", "    x = Conv2D(2, (1,1), padding='same')(x)\n", "    \n", "\n", "    model = Model(inputs=mnv3.input, outputs=x)\n", "    \n", "    model.compile(optimizer=<PERSON>(lr=1e-4),\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy']) # Ensure you have sparse labels\n", "    \n", "    return model\n", "\n", "model=mnv3_unet(finetune=True, pretrained=False, alpha=1)\n", "\n", "# Model summary\n", "model.summary()\n", "\n", "# Layer specifications\n", "for i, layer in enumerate(model.layers):\n", "    print(i, layer.output.name, layer.output.shape, layer.trainable)\n"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "0L4mW7gc0zrw"}, "source": ["**Note:** The official tf.keras mobilenetv3 architecture contains a '**Rescale**' layer in the network architecture for normalizing input images. However, it was observed that this layer is neither supported by tflite **GPU** delegate nor by tf.keras **quantization** API's. Therefore we remove this layer and directly normalize the images in the dataloader module, before feeding it to the network."]}, {"cell_type": "markdown", "metadata": {"id": "zWNtXMgOEsgy"}, "source": ["**6. <PERSON> Loader And Augmentations**"]}, {"cell_type": "markdown", "metadata": {"id": "GJIsouRIzJmc"}, "source": ["Use the custom data-loader to perform augmentaion on-the-fly and feed the network with batch of images. Here we use the augmentaion like **brightness, saturation, contrast, cropping, flipping** etc for training the model. The mask images should be **binary** with pixel value **0** representing **background**, and stored in **png** format."]}, {"cell_type": "code", "metadata": {"id": "y68PsEfL39K9"}, "source": ["import tensorflow as tf\n", "import random\n", "\n", "class DataLoader(object):\n", "    \"\"\"A TensorFlow Dataset API based loader for semantic segmentation problems.\"\"\"\n", "\n", "    def __init__(self, image_paths, mask_paths, image_size, channels=[3, 3], crop_percent=None, palette=None, seed=None):\n", "        \"\"\"\n", "        Initializes the data loader object\n", "        Args:\n", "            image_paths: List of paths of train images.\n", "            mask_paths: List of paths of train masks (segmentation masks)\n", "            image_size: Tuple of (Height, Width), the final height \n", "                        of the loaded images.\n", "            channels: List of ints, first element is number of channels in images,\n", "                      second is the number of channels in the mask image (needed to\n", "                      correctly read the images into tensorflow.)\n", "            crop_percent: Float in the range 0-1, defining percentage of image \n", "                          to randomly crop.\n", "            palette: A list of RGB pixel values in the mask. If specified, the mask\n", "                     will be one hot encoded along the channel dimension.\n", "            seed: An int, if not specified, chosen randomly. Used as the seed for \n", "                  the RNG in the data pipeline.\n", "        \"\"\"\n", "        self.image_paths = image_paths\n", "        self.mask_paths = mask_paths\n", "        self.palette = palette\n", "        self.image_size = image_size\n", "        if crop_percent is not None:\n", "            if 0.0 < crop_percent <= 1.0:\n", "                self.crop_percent = tf.constant(crop_percent, tf.float32)\n", "            elif 0 < crop_percent <= 100:\n", "                self.crop_percent = tf.constant(crop_percent / 100., tf.float32)\n", "            else:\n", "                raise ValueError(\"Invalid value entered for crop size. Please use an \\\n", "                                  integer between 0 and 100, or a float between 0 and 1.0\")\n", "        else:\n", "            self.crop_percent = None\n", "        self.channels = channels\n", "        if seed is None:\n", "            self.seed = random.randint(0, 1000)\n", "        else:\n", "            self.seed = seed\n", "\n", "    def _corrupt_brightness(self, image, mask):\n", "        \"\"\"\n", "        <PERSON><PERSON><PERSON><PERSON> applies a random brightness change.\n", "        \"\"\"\n", "        cond_brightness = tf.cast(tf.random.uniform(\n", "            [], maxval=2, dtype=tf.int32), tf.bool)\n", "        image = tf.cond(cond_brightness, lambda: tf.image.random_brightness(\n", "            image, 0.1), lambda: tf.identity(image))\n", "        return image, mask\n", "\n", "\n", "    def _corrupt_contrast(self, image, mask):\n", "        \"\"\"\n", "        Randomly applies a random contrast change.\n", "        \"\"\"\n", "        cond_contrast = tf.cast(tf.random.uniform(\n", "            [], maxval=2, dtype=tf.int32), tf.bool)\n", "        image = tf.cond(cond_contrast, lambda: tf.image.random_contrast(\n", "            image, 0.1, 0.8), lambda: tf.identity(image))\n", "        return image, mask\n", "\n", "\n", "    def _corrupt_saturation(self, image, mask):\n", "        \"\"\"\n", "        Randomly applies a random saturation change.\n", "        \"\"\"\n", "        cond_saturation = tf.cast(tf.random.uniform(\n", "            [], maxval=2, dtype=tf.int32), tf.bool)\n", "        image = tf.cond(cond_saturation, lambda: tf.image.random_saturation(\n", "            image, 0.1, 0.8), lambda: tf.identity(image))\n", "        return image, mask\n", "\n", "\n", "    def _crop_random(self, image, mask):\n", "        \"\"\"\n", "        Randomly crops image and mask in accord.\n", "        \"\"\"\n", "        cond_crop_image = tf.cast(tf.random.uniform(\n", "            [], maxval=2, dtype=tf.int32, seed=self.seed), tf.bool)\n", "        cond_crop_mask = tf.cast(tf.random.uniform(\n", "            [], maxval=2, dtype=tf.int32, seed=self.seed), tf.bool)\n", "\n", "        shape = tf.cast(tf.shape(image), tf.float32)\n", "        h = tf.cast(shape[0] * self.crop_percent, tf.int32)\n", "        w = tf.cast(shape[1] * self.crop_percent, tf.int32)\n", "\n", "        image = tf.cond(cond_crop_image, lambda: tf.image.random_crop(\n", "            image, [h, w, self.channels[0]], seed=self.seed), lambda: tf.identity(image))\n", "        mask = tf.cond(cond_crop_mask, lambda: tf.image.random_crop(\n", "            mask, [h, w, self.channels[1]], seed=self.seed), lambda: tf.identity(mask))\n", "\n", "        return image, mask\n", "\n", "\n", "    def _flip_left_right(self, image, mask):\n", "        \"\"\"\n", "        Randomly flips image and mask left or right in accord.\n", "        \"\"\"\n", "        image = tf.image.random_flip_left_right(image, seed=self.seed)\n", "        mask = tf.image.random_flip_left_right(mask, seed=self.seed)\n", "\n", "        return image, mask\n", "\n", "\n", "    def _resize_data(self, image, mask):\n", "        \"\"\"\n", "        Resizes images to specified size and normalizes the image: [0...1]\n", "        \"\"\"\n", "        image = tf.image.resize(image, [self.image_size, self.image_size]) #/255.0\n", "        image = tf.keras.applications.mobilenet.preprocess_input(image) # For mobilnetv3 preprocesing in tf-nightly\n", "        mask = tf.image.resize(mask, [self.image_size, self.image_size], method='nearest')//tf.reduce_max(mask) # masks should be binary with 0 representing background\n", "      \n", "        return image, mask\n", "\n", "\n", "    def _parse_data(self, image_paths, mask_paths):\n", "        \"\"\"\n", "        Reads image and mask files depending on\n", "        specified extension.\n", "        \"\"\"\n", "        image_content = tf.io.read_file(image_paths)\n", "        mask_content = tf.io.read_file(mask_paths)\n", "\n", "        images = tf.image.decode_jpeg(image_content, channels=self.channels[0])\n", "        masks = tf.image.decode_jpeg(mask_content, channels=self.channels[1])\n", "\n", "        return images, masks\n", "\n", "    def data_batch(self, batch_size, augment, shuffle=False, one_hot_encode=False):\n", "        \"\"\"\n", "        Reads data, normalizes it, shuffles it, then batches it, returns a\n", "        the next element in dataset op and the dataset initializer op.\n", "        Inputs:\n", "            batch_size: Number of images/masks in each batch returned.\n", "            augment: Boolean, whether to augment data or not.\n", "            shuffle: <PERSON><PERSON>an, whether to shuffle data in buffer or not.\n", "            one_hot_encode: Boolean, whether to one hot encode the mask image or not.\n", "                            Encoding will done according to the palette specified when\n", "                            initializing the object.\n", "        Returns:\n", "            data: A tf dataset object.\n", "        \"\"\"\n", "\n", "        # Create dataset out of the 2 files:\n", "        data = tf.data.Dataset.from_tensor_slices((self.image_paths, self.mask_paths))\n", "\n", "        # Parse images and labels\n", "        data = data.map(self._parse_data, num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "        # If augmentation is to be applied\n", "        if augment:\n", "            data = data.map(self._corrupt_brightness,\n", "                            num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "            data = data.map(self._corrupt_contrast,\n", "                            num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "            data = data.map(self._corrupt_saturation,\n", "                            num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "            if self.crop_percent is not None:\n", "                data = data.map(self._crop_random, \n", "                                num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "            data = data.map(self._flip_left_right,\n", "                            num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "        # Resize to smaller dims for speed\n", "        data = data.map(self._resize_data, num_parallel_calls=tf.data.experimental.AUTOTUNE)\n", "\n", "        if shuffle:\n", "            # Shuffle, repeat, batch and prefetch\n", "            data = data.shuffle(1000).repeat().batch(batch_size).prefetch(tf.data.experimental.AUTOTUNE)\n", "        else:\n", "            # Batch and prefetch\n", "            data = data.repeat().batch(batch_size).prefetch(tf.data.experimental.AUTOTUNE)\n", "\n", "        return data"], "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "AEWQsWf-0GAl"}, "source": ["**Note:** The keras **preprocessing layer** for mobilenetv3 scales the input image into range: **[-1.0, 1.0].**"]}, {"cell_type": "markdown", "metadata": {"id": "FocKKfBBAW00"}, "source": ["\n", "Configure the **data loader, image paths and logging** options."]}, {"cell_type": "code", "metadata": {"id": "JVj3_v___hm0"}, "source": ["import tensorflow as tf\n", "import os\n", "\n", "IMAGE_DIR_PATH = '/content/JPEGImages'\n", "MASK_DIR_PATH = '/content/SegmentationClassRaw'\n", "\n", "image_paths = [os.path.join(IMAGE_DIR_PATH, x) for x in sorted(os.listdir(IMAGE_DIR_PATH)) if x.endswith('.jpg')]\n", "mask_paths = [os.path.join(MASK_DIR_PATH, x) for x in sorted(os.listdir(MASK_DIR_PATH)) if x.endswith('.png')]\n", "\n", "train_image_paths, val_image_paths, train_mask_paths, val_mask_paths = train_test_split(image_paths, mask_paths, test_size = 0.2, random_state = 0)\n", "\n", "CHECKPOINT=\"/content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-{epoch:02d}-{val_loss:.2f}.hdf5\"\n", "LOGS='./logs'\n", "\n", "num_train=len(train_image_paths)\n", "num_val=len(val_image_paths)\n", "batch_sz=64\n", "epochs=100\n", "\n", "\n", "# Initialize the dataloader object\n", "train_dataset = DataLoader(image_paths=train_image_paths,\n", "                     mask_paths=train_mask_paths,\n", "                     image_size=256,\n", "                     crop_percent=0.8,\n", "                     channels=[3, 1],\n", "                     seed=47)\n", "val_dataset = DataLoader(image_paths=val_image_paths,\n", "                     mask_paths=val_mask_paths,\n", "                     image_size=256,\n", "                     crop_percent=0.8,\n", "                     channels=[3, 1],\n", "                     seed=47)\n", "\n", "# Parse the images and masks, and return the data in batches, augmented optionally.\n", "train_dataset = train_dataset.data_batch(batch_size=batch_sz,\n", "                             augment=True, \n", "                             shuffle=True)\n", "val_dataset = val_dataset.data_batch(batch_size=batch_sz,\n", "                             augment=False, \n", "                             shuffle=True)"], "execution_count": 7, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "PUNQ_HpBBShB"}, "source": ["**7. Training and Callbacks**\n", "\n", "Configure the callbacks for learning rate decay, logging and checkpoint."]}, {"cell_type": "code", "metadata": {"id": "DoXXz34F0SCP"}, "source": ["# Save checkpoints\n", "checkpoint = ModelCheckpoint(CHECKPOINT, monitor='val_loss', verbose=1, save_weights_only=False , save_best_only=True, mode='min')\n", "\n", "# Callbacks \n", "reduce_lr = ReduceLROnPlateau(factor=0.5, patience=5, min_lr=0.000001, verbose=1)\n", "tensorboard = TensorBoard(log_dir=LOGS, histogram_freq=0,\n", "                          write_graph=True, write_images=True)\n", "\n", "callbacks_list = [checkpoint, tensorboard, reduce_lr]"], "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "iAZs1jYqBZI5"}, "source": ["Perform training and validation on the model, using the dataset and save the results."]}, {"cell_type": "code", "metadata": {"id": "OaMsm8Ss0nJL"}, "source": ["# Train the model for few epochs, with frozen encoder\n", "model_history = model.fit(train_dataset, epochs=epochs,\n", "                          steps_per_epoch=num_train//batch_sz,\n", "                          validation_steps=num_val//batch_sz,\n", "                          validation_data=val_dataset,\n", "                          callbacks=callbacks_list)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "IFckI9p4K6BC"}, "source": ["**Note:** The encoder will not be trained during the initial training proces."]}, {"cell_type": "markdown", "metadata": {"id": "ZLjlU4yZCarS"}, "source": ["Finally, **unfreeze** all the layers and train the model for another 20 epoochs."]}, {"cell_type": "code", "metadata": {"id": "LdJDLwvPIPus"}, "source": ["# Unfreeze all the layers of the pretrained model\n", "model=mnv3_unet(finetune=False, pretrained=True, alpha=1)\n", "\n", "\n", "# Resume training from previous checkpoint\n", "model_history = model.fit(train_dataset, epochs=epochs,\n", "                          steps_per_epoch=num_train//batch_sz,\n", "                          validation_steps=num_val//batch_sz,\n", "                          validation_data=val_dataset,\n", "                          callbacks=callbacks_list)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "SoqznZWJF7Md"}, "source": ["**8. Quantization Aware Training**\n", "\n", "Quantization aware training emulates inference-time quantization, creating a model that downstream tools will use to produce actually quantized models. The quantized models use lower-precision (e.g. **8-bit** instead of 32-bit float), leading to benefits during deployment.\n", "\n", "**Advantages:-**\n", "*   Persistence of accuracy (same)\n", "*   Latency benefits  (faster)\n", "*   Smaller model size (4x)\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "MIXja8-1FVcf"}, "source": ["Install tensorflow **model optimization** from source, using bazel."]}, {"cell_type": "code", "metadata": {"id": "hn-dDJQbFMsh"}, "source": ["!git clone https://github.com/tensorflow/model-optimization.git\n", "%cd model-optimization\n", "!bazel build --copt=-O3 --copt=-march=native :pip_pkg\n", "!./bazel-bin/pip_pkg pkgdir\n", "!pip install --user --upgrade pkgdir/*.whl"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "6QYWP394DJep"}, "source": ["**Note:** Here we need to install the latest **tfmot version** from source, since the the quantization support for **Upsampling2D** operator is only available in this version."]}, {"cell_type": "markdown", "metadata": {"id": "zOzGquyDFe0Q"}, "source": ["Load the trained **float model** checkpoint."]}, {"cell_type": "code", "metadata": {"id": "MSsjtdQzEOPg"}, "source": ["import tensorflow_model_optimization as tfmot\n", "\n", "float_model=load_model('/content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-13-0.03.hdf5') # Final float model\n", "\n", "float_model.summary()"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "qeDUThNCGOsR"}, "source": ["Load the **quantized model** checkpoint, if any."]}, {"cell_type": "code", "metadata": {"id": "Pc2Vz_mnql86"}, "source": ["with tfmot.quantization.keras.quantize_scope():\n", "    quant_model = tf.keras.models.load_model('/content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-09-0.03.hdf5') # Final quant model\n", "    \n", "quant_model.summary()"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "lcOdg8jRL3gX"}, "source": ["Create the **quantization aware model** from float model and **compile** it using the same loss function."]}, {"cell_type": "code", "metadata": {"id": "nw0rSvo6HLSQ"}, "source": ["import tensorflow_model_optimization as tfmot\n", "\n", "quantize_model = tfmot.quantization.keras.quantize_model\n", "\n", "# q_aware stands for for quantization aware.\n", "q_aware_model = quantize_model(float_model)\n", "\n", "# `quantize_model` requires a recompile.\n", "q_aware_model.compile(optimizer=<PERSON>(lr=1e-4),\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])\n", "\n", "q_aware_model.summary()"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "CfM0JuEIKM_N"}, "source": ["Perform **quantization aware training** on the quant aware model, using the training dataset for **10 epochs**."]}, {"cell_type": "code", "metadata": {"id": "rUErLWeD1Kkk", "outputId": "3d11a8f2-61d9-473d-e56c-a6c9fa3457fa", "colab": {"base_uri": "https://localhost:8080/"}}, "source": ["q_aware_model.fit(train_dataset, epochs=10,\n", "                          steps_per_epoch=num_train//batch_sz,\n", "                          validation_steps=num_val//batch_sz,\n", "                          validation_data=val_dataset,\n", "                          callbacks=callbacks_list)"], "execution_count": 13, "outputs": [{"output_type": "stream", "text": ["Epoch 1/10\n", "860/860 [==============================] - 609s 677ms/step - loss: 0.3826 - accuracy: 0.8718 - val_loss: 0.0302 - val_accuracy: 0.9884\n", "\n", "Epoch 00001: val_loss improved from inf to 0.03022, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-01-0.03.hdf5\n", "Epoch 2/10\n", "860/860 [==============================] - 570s 663ms/step - loss: 0.0404 - accuracy: 0.9846 - val_loss: 0.0288 - val_accuracy: 0.9889\n", "\n", "Epoch 00002: val_loss improved from 0.03022 to 0.02877, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-02-0.03.hdf5\n", "Epoch 3/10\n", "860/860 [==============================] - 569s 661ms/step - loss: 0.0403 - accuracy: 0.9848 - val_loss: 0.0284 - val_accuracy: 0.9891\n", "\n", "Epoch 00003: val_loss improved from 0.02877 to 0.02838, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-03-0.03.hdf5\n", "Epoch 4/10\n", "860/860 [==============================] - 572s 666ms/step - loss: 0.0402 - accuracy: 0.9850 - val_loss: 0.0281 - val_accuracy: 0.9891\n", "\n", "Epoch 00004: val_loss improved from 0.02838 to 0.02812, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-04-0.03.hdf5\n", "Epoch 5/10\n", "860/860 [==============================] - 575s 669ms/step - loss: 0.0360 - accuracy: 0.9860 - val_loss: 0.0274 - val_accuracy: 0.9895\n", "\n", "Epoch 00005: val_loss improved from 0.02812 to 0.02736, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-05-0.03.hdf5\n", "Epoch 6/10\n", "860/860 [==============================] - 580s 675ms/step - loss: 0.0350 - accuracy: 0.9864 - val_loss: 0.0274 - val_accuracy: 0.9895\n", "\n", "Epoch 00006: val_loss did not improve from 0.02736\n", "Epoch 7/10\n", "860/860 [==============================] - 588s 684ms/step - loss: 0.0349 - accuracy: 0.9866 - val_loss: 0.0266 - val_accuracy: 0.9897\n", "\n", "Epoch 00007: val_loss improved from 0.02736 to 0.02663, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-07-0.03.hdf5\n", "Epoch 8/10\n", "860/860 [==============================] - 548s 638ms/step - loss: 0.0334 - accuracy: 0.9870 - val_loss: 0.0263 - val_accuracy: 0.9898\n", "\n", "Epoch 00008: val_loss improved from 0.02663 to 0.02630, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-08-0.03.hdf5\n", "Epoch 9/10\n", "860/860 [==============================] - 580s 675ms/step - loss: 0.0331 - accuracy: 0.9872 - val_loss: 0.0266 - val_accuracy: 0.9898\n", "\n", "Epoch 00009: val_loss did not improve from 0.02630\n", "Epoch 10/10\n", "860/860 [==============================] - 553s 643ms/step - loss: 0.0324 - accuracy: 0.9876 - val_loss: 0.0260 - val_accuracy: 0.9900\n", "\n", "Epoch 00010: val_loss improved from 0.02630 to 0.02603, saving model to /content/drive/MyDrive/mnv3seg/ckpt/mnv3seg-10-0.03.hdf5\n"], "name": "stdout"}, {"output_type": "execute_result", "data": {"text/plain": ["<tensorflow.python.keras.callbacks.History at 0x7f26317c8550>"]}, "metadata": {"tags": []}, "execution_count": 13}]}, {"cell_type": "markdown", "metadata": {"id": "1zBGS1aRKmwP"}, "source": ["**9. Tflite Conversion**"]}, {"cell_type": "markdown", "metadata": {"id": "9DVSk6kJNiMd"}, "source": ["Convert the **float mode**l to tflite format.\n", "\n"]}, {"cell_type": "code", "metadata": {"id": "zl2M07mkK3q4"}, "source": ["# Fix the model input batch size to 1\n", "input_name = float_model.input_names[0]\n", "index = float_model.input_names.index(input_name)\n", "float_model.inputs[index].set_shape([1, 256, 256, 3])\n", "\n", "\n", "converter = tf.lite.TFLiteConverter.from_keras_model(float_model)\n", "tflite_model = converter.convert()\n", "open(\"mnv3_seg_float.tflite\", \"wb\").write(tflite_model)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "2F5TJckdNm69"}, "source": ["Convert the **quantized model** to tflite format."]}, {"cell_type": "code", "metadata": {"id": "1jioYT6IMAPq"}, "source": ["# Fix the model input batch size to 1\n", "input_name = quant_model.input_names[0]\n", "index = quant_model.input_names.index(input_name)\n", "quant_model.inputs[index].set_shape([1, 256, 256, 3])\n", "\n", "# Convert the model to tflite format\n", "converter = tf.lite.TFLiteConverter.from_keras_model(quant_model)\n", "converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "quantized_tflite_model = converter.convert()\n", "open(\"mnv3_seg_quant.tflite\", \"wb\").write(quantized_tflite_model)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "9X54gpy4KLMj"}, "source": ["**Notes**:-\n", "1. In the new converter, if you don't want dynamic batch in the final model, then set the model before converting to have a **static shape**. Otherwise it will create '**Shape, StridedSlice and Pack**' layers, which are **not supported** by hardware accelerators like GPU or DSP.\n", "\n", "2. The current quantized tflite model would still have **float** input and outputs for convenience. But, unfortunately we can't run them on hardware accelerators like hexagon DSP or coral, since they are not yet fully quantized i.e **full integer quantized**."]}, {"cell_type": "markdown", "metadata": {"id": "udb9dVKAS5UM"}, "source": ["**10. Post Training Quantization**"]}, {"cell_type": "markdown", "metadata": {"id": "CIHH7lCAVM6T"}, "source": ["We can also reduce the model size and **latency** by performing post training quantization. A fully quantized model with **UINT8 inputs and outputs** can take advantage of **accelerators** like Coral TPU, Hexagon DSP etc. "]}, {"cell_type": "code", "metadata": {"id": "fb8ZbVFJSsdQ"}, "source": ["# Create a representative dataset from training set\n", "representative_images, _ = next(iter(train_dataset))\n", "\n", "def representative_dataset_gen():\n", "    for image in representative_images:\n", "        yield [tf.expand_dims(image, 0)]\n", "\n", "# Configure the converter with UINT8 inputs and outputs\n", "converter = tf.lite.TFLiteConverter.from_keras_model(float_model)\n", "converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "converter.representative_dataset = representative_dataset_gen\n", "converter.target_spec.supported_ops = [tf.lite.OpsSet.TFLITE_BUILTINS_INT8]\n", "converter.inference_input_type = tf.uint8\n", "converter.inference_output_type = tf.uint8\n", "\n", "# Convert and save the tflite model\n", "post_quantized_tflite_model = converter.convert()\n", "open(\"mnv3_post_quant.tflite\", \"wb\").write(post_quantized_tflite_model)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "1PVJWOSrVzia"}, "source": ["**Note:** Compared to a QAT model, a PTQ model may have lower accuracy"]}, {"cell_type": "markdown", "metadata": {"id": "hVS4eQqneUyG"}, "source": ["Now apply **post training quantization on top of QAT model** with the help of representative dataset. This will convert the input and output of the original QAT model to **UINT8** format, resulting in a **full integer model** that can be run on hardware **accelerators** like coral-tpu, hexagon dsp etc."]}, {"cell_type": "code", "metadata": {"id": "ndPKNinocbrH"}, "source": ["# Create a representative dataset from training set\n", "representative_images, _ = next(iter(train_dataset))\n", "\n", "def representative_dataset_gen():\n", "    for image in representative_images:\n", "        yield [tf.expand_dims(image, 0)]\n", "\n", "# Configure the converter with UINT8 inputs and outputs\n", "converter = tf.lite.TFLiteConverter.from_keras_model(quant_model) # Use quant model\n", "converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "converter.representative_dataset = representative_dataset_gen\n", "converter.target_spec.supported_ops = [tf.lite.OpsSet.TFLITE_BUILTINS_INT8]\n", "converter._experimental_new_quantizer = True\n", "converter.inference_input_type = tf.uint8\n", "converter.inference_output_type = tf.uint8\n", "\n", "# Convert and save the tflite model\n", "post_quantized_aware_tflite_model = converter.convert()\n", "open(\"mnv3_post_quant_aware.tflite\", \"wb\").write(post_quantized_aware_tflite_model)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "SuABZwIyjCZy"}, "source": ["**Note:** We have to add the converter option **converter._experimental_new_quantizer = True** on tf versions >= 2.4.0-rc1, for successfully converting the model to full integer (UINT8) format. Otherwise, the converter seems to throw an error: **RuntimeError: Quantization not yet supported for op: 'DEQUANTIZE'**."]}, {"cell_type": "markdown", "metadata": {"id": "rIP7ZCRAAvGw"}, "source": ["**11. Testing**"]}, {"cell_type": "markdown", "metadata": {"id": "0YRpGcLjPnrI"}, "source": ["Run the **tflite models** on test images, using tflite **interpreter** and plot the results using matplotlib."]}, {"cell_type": "code", "metadata": {"id": "htbDpzoAAxKo"}, "source": ["def run_tflite_model(tflite_file, test_image):\n", "\n", "  # Initialize the interpreter\n", "  interpreter = tf.lite.Interpreter(model_path=str(tflite_file))\n", "  interpreter.allocate_tensors()\n", "\n", "  # Get input and output details\n", "  input_details = interpreter.get_input_details()[0]\n", "  output_details = interpreter.get_output_details()[0]\n", "\n", "  # Preprocess the input image\n", "  test_image = (test_image - 127.5) / 127.5\n", "  if input_details['dtype'] == np.uint8:\n", "      input_scale, input_zero_point = input_details[\"quantization\"]\n", "      test_image = test_image / input_scale + input_zero_point\n", "  test_image = np.expand_dims(test_image, axis=0).astype(input_details[\"dtype\"])\n", "\n", "  # Run the interpreter and get the output\n", "  interpreter.set_tensor(input_details[\"index\"], test_image)\n", "  interpreter.invoke()\n", "  output = interpreter.get_tensor(output_details[\"index\"])[0]\n", "\n", "  # Compute mask from segmentaion output\n", "  mask = np.argmax(output, axis=-1)\n", "\n", "  return mask"], "execution_count": 17, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "8TgdnnjkQUAt"}, "source": ["Run the **float model** on test image and plot the results."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "AtmJw55UGvS-", "outputId": "f0348368-85e1-4004-d70c-15c2782822ba"}, "source": ["image=np.array(Image.open('/content/port_0basketball-player-game-sport-159611.jpeg'))\n", "mask=run_tflite_model(tflite_file='/content/model-optimization/mnv3_seg_float.tflite',test_image=image)\n", "crop_float=image*mask[...,np.newaxis]\n", "plt.imshow(crop_float)\n", "plt.title('Float Model Output')\n", "plt.show()"], "execution_count": 20, "outputs": [{"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "8qOChXv3QmVD"}, "source": ["Run the **quantization aware trained model** on test image and plot the results."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "GqmE3aBfJyrU", "outputId": "5796613e-be4b-44bf-e334-d944a6883c1a"}, "source": ["image=np.array(Image.open('/content/port_0basketball-player-game-sport-159611.jpeg'))\n", "mask=run_tflite_model(tflite_file='/content/model-optimization/mnv3_seg_quant.tflite',test_image=image)\n", "crop_qat=image*mask[...,np.newaxis]\n", "plt.imshow(crop_qat)\n", "plt.title('QAT Model Output')\n", "plt.show()"], "execution_count": 21, "outputs": [{"output_type": "display_data", "data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "G1olHeLcQ38m"}, "source": ["Run the **post training quantized model** on test image and plot the results."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "hlNOrMGMLYjW", "outputId": "7e339dfe-a5ed-42d4-cc48-9a45270cf859"}, "source": ["image=np.array(Image.open('/content/port_0basketball-player-game-sport-159611.jpeg'))\n", "mask=run_tflite_model(tflite_file='/content/model-optimization/mnv3_post_quant.tflite',test_image=image)\n", "crop_ptq=image*mask[...,np.newaxis]\n", "plt.imshow(crop_ptq)\n", "plt.title('PTQ Model Output')\n", "plt.show()"], "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "RXKBAEOgiCJZ"}, "source": ["Run the **post training quantization aware model** on test image and plot the results."]}, {"cell_type": "code", "metadata": {"id": "zUk_U61chdRs", "outputId": "8700760b-8925-4668-e280-41ffcd78b3f8", "colab": {"base_uri": "https://localhost:8080/", "height": 281}}, "source": ["image=np.array(Image.open('/content/port_0basketball-player-game-sport-159611.jpeg'))\n", "mask=run_tflite_model(tflite_file='/content/model-optimization/mnv3_post_quant_aware.tflite',test_image=image)\n", "crop_ptq=image*mask[...,np.newaxis]\n", "plt.imshow(crop_ptq)\n", "plt.title('PT-QAT Model Output')\n", "plt.show()"], "execution_count": 26, "outputs": [{"output_type": "display_data", "data": {"image/png": "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*************************************************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "z6z8twJGmc58"}, "source": ["**References:-**\n", "\n", "\n", "\n", "1.   https://www.tensorflow.org/install/source#setup_for_linux_and_macos\n", "2.   https://www.tensorflow.org/model_optimization/guide/install\n", "3. https://github.com/tensorflow/tensorflow/blob/master/tensorflow/python/keras/applications/mobilenet_v3.py\n", "4. https://www.tensorflow.org/model_optimization/guide/quantization/training_example\n", "5. https://www.tensorflow.org/lite/performance/post_training_integer_quant\n", "6. https://github.com/tensorflow/tensorflow/issues/42082\n", "7. https://github.com/HasnainRaz/SemSegPipeline\n", "8. https://github.com/tensorflow/tensorflow/issues/42082\n", "\n"]}]}