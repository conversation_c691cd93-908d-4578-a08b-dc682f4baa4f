<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en" sourcelanguage="zh">
<context>
    <name>APP_EISeg</name>
    <message>
        <location filename="../../eiseg/app.py" line="298"/>
        <source>&amp;打开图像</source>
        <translation>&amp;Open Image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="298"/>
        <source>打开一张图像进行标注</source>
        <translation>Open an image for annotation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="305"/>
        <source>&amp;打开文件夹</source>
        <translation>&amp;Open Dir</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="305"/>
        <source>打开一个文件夹下所有的图像进行标注</source>
        <translation>Open all images in a folder for annotation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="312"/>
        <source>&amp;改变标签保存路径</source>
        <translation>&amp;Change Output Dir</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="312"/>
        <source>改变标签保存的文件夹路径</source>
        <translation>Change the folder where labels are saved</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="319"/>
        <source>&amp;加载模型参数</source>
        <translation>&amp;Load Model Parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="319"/>
        <source>加载一个模型参数</source>
        <translation>Load a model parameter</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="326"/>
        <source>&amp;保存</source>
        <translation>&amp;Save</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="326"/>
        <source>保存图像标签</source>
        <translation>Save image label</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="333"/>
        <source>&amp;另存为</source>
        <translation>&amp;Save as</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="333"/>
        <source>在指定位置另存为标签</source>
        <translation>Save as label at the specified location</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="340"/>
        <source>&amp;自动保存</source>
        <translation>&amp;Auto Save</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="340"/>
        <source>翻页同时自动保存</source>
        <translation>ave automatically while turning image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="351"/>
        <source>&amp;上一张</source>
        <translation>&amp;Prev Image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="351"/>
        <source>翻到上一张图片</source>
        <translation>Filp to previous image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="358"/>
        <source>&amp;下一张</source>
        <translation>&amp;Next Image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="358"/>
        <source>翻到下一张图片</source>
        <translation>Flip to the next image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="365"/>
        <source>&amp;完成当前目标</source>
        <translation>&amp;Finish Current Target</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="365"/>
        <source>完成当前目标的标注</source>
        <translation>Finish labeling the current object</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="372"/>
        <source>&amp;清除所有标注</source>
        <translation>&amp;Clear All Labels</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="372"/>
        <source>清除所有标注信息</source>
        <translation>Clear all labels in the image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="379"/>
        <source>&amp;撤销</source>
        <translation>&amp;Undo</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="379"/>
        <source>撤销一次点击</source>
        <translation>Undo one click</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="386"/>
        <source>&amp;重做</source>
        <translation>&amp;Redo</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="386"/>
        <source>重做一次点击</source>
        <translation>Redo one click</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="393"/>
        <source>&amp;删除多边形</source>
        <translation>&amp;Delete Polygon</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="393"/>
        <source>删除当前选中的多边形</source>
        <translation>Deletes the currently selected polygon</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="400"/>
        <source>&amp;删除所有多边形</source>
        <translation>&amp;Delete all polygons</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="400"/>
        <source>删除所有的多边形</source>
        <translation>Delete all polygons</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="407"/>
        <source>&amp;保留最大连通块</source>
        <translation>&amp;Filter LCC</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="407"/>
        <source>保留最大的连通块</source>
        <translation>Keep the largest connected component only</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="415"/>
        <source>&amp;标签和图像使用相同拓展名</source>
        <translation>&amp;Use same extension name</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="415"/>
        <source>标签和图像使用相同拓展名，用于图像中有文件名相同但拓展名不同的情况，防止标签覆盖</source>
        <translation>The label and image use the same extension name, which is used to prevent the label from being overwritten when the file name in the image is the same but the extension name is different</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="423"/>
        <source>&amp;伪彩色保存</source>
        <translation>&amp;Pseudo Color Format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="423"/>
        <source>保存为伪彩色图像</source>
        <translation>Save label in pseudo color format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="432"/>
        <source>&amp;灰度保存</source>
        <translation>&amp;Grayscale Format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="432"/>
        <source>保存为灰度图像，像素的灰度为对应类型的标签</source>
        <translation>Save label in grayscale format, the value of each pixel is the id for the label category of the pixel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="441"/>
        <source>&amp;JSON保存</source>
        <translation>&amp;JSON Format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="441"/>
        <source>保存为JSON格式</source>
        <translation>Save polygon information in JSON format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="450"/>
        <source>&amp;COCO保存</source>
        <translation>&amp;Coco Format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="450"/>
        <source>保存为COCO格式</source>
        <translation>Save polygon information in coco format</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="460"/>
        <source>&amp;显示遥感多边形</source>
        <translation>&amp;Display RS polygons</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="460"/>
        <source>显示遥感大图的多边形结果</source>
        <translation>Display RS large polygon</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="469"/>
        <source>&amp;启用宫格检测</source>
        <translation>&amp;Enable grid detection</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="469"/>
        <source>针对每张图片启用宫格检测</source>
        <translation>Enable grid detection for each picture</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="478"/>
        <source>&amp;抠图保存</source>
        <translation>&amp;Save Matting</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="478"/>
        <source>只保留前景，背景设置为背景色</source>
        <translation>Only keep foreground pixels, set all background pixels to background color</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="487"/>
        <source>&amp;设置抠图背景色</source>
        <translation>&amp;Set matting background color</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="487"/>
        <source>抠图后背景像素的颜色</source>
        <translation>The color to use for all background pixels</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="494"/>
        <source>&amp;关闭</source>
        <translation>&amp;Close</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="494"/>
        <source>关闭当前图像</source>
        <translation>Close current image</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="501"/>
        <source>&amp;退出</source>
        <translation>&amp;Exit</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="501"/>
        <source>退出软件</source>
        <translation>Exit software</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="508"/>
        <source>&amp;导出标签列表</source>
        <translation>&amp;Export label list</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="508"/>
        <source>将标签列表导出成标签配置文件</source>
        <translation>Export label list to label profile</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="515"/>
        <source>&amp;载入标签列表</source>
        <translation>&amp;Load label list</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="515"/>
        <source>从标签配置文件载入标签列表</source>
        <translation>Load label list from label profile</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="522"/>
        <source>&amp;清空标签列表</source>
        <translation>&amp;Clear Label List</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="522"/>
        <source>清空所有的标签</source>
        <translation>Clear all labels</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="529"/>
        <source>&amp;清除近期文件记录</source>
        <translation>&amp;Clear recent file records</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="529"/>
        <source>清除近期标注文件记录</source>
        <translation>Clear recent annotation file records</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="536"/>
        <source>&amp;模型选择</source>
        <translation>&amp;Model Selection</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="536"/>
        <source>隐藏/展示模型选择面板</source>
        <translation>Hide / show model selection panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="544"/>
        <source>&amp;数据列表</source>
        <translation>&amp;Image List</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="544"/>
        <source>隐藏/展示数据列表面板</source>
        <translation>Hide / show data list panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="552"/>
        <source>&amp;标签列表</source>
        <translation>&amp;Label List</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="552"/>
        <source>隐藏/展示标签列表面板</source>
        <translation>Hide / show label list panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="560"/>
        <source>&amp;分割设置</source>
        <translation>&amp;Segmentation Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="560"/>
        <source>隐藏/展示分割设置面板</source>
        <translation>Hide / show split settings panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="568"/>
        <source>&amp;遥感设置</source>
        <translation>&amp;Remote sensing Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="568"/>
        <source>隐藏/展示遥感设置面板</source>
        <translation>Hide / show remote sensing settings panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="576"/>
        <source>&amp;医疗设置</source>
        <translation>&amp;Medical Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="576"/>
        <source>隐藏/展示医疗设置面板</source>
        <translation>Hide / show medical settings panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="584"/>
        <source>&amp;N2宫格标注</source>
        <translation>&amp;N2 Grid Label</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="584"/>
        <source>隐藏/展示N^2宫格细粒度标注面板</source>
        <translation>Hide / show n ^ 2 grid fine-grained dimension panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="592"/>
        <source>&amp;视频播放</source>
        <translation>&amp;Video Player</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="592"/>
        <source>隐藏/展示视频播放面板</source>
        <translation>Hide / show video player panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="600"/>
        <source>&amp;视频标注</source>
        <translation>&amp;Video Annotation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="600"/>
        <source>隐藏/展示视频标注面板</source>
        <translation>Hide / show video annotation panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="608"/>
        <source>&amp;3D显示</source>
        <translation>&amp;3D Display</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="608"/>
        <source>隐藏/展示3D显示面板</source>
        <translation>Hide / show 3D display panel</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="616"/>
        <source>&amp;快速入门</source>
        <translation>&amp;Quick start</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="616"/>
        <source>主要功能使用介绍</source>
        <translation>Introduction to main functions</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="623"/>
        <source>&amp;反馈问题</source>
        <translation>&amp;Feedback issues</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="623"/>
        <source>通过Github Issue反馈使用过程中遇到的问题。我们会尽快进行修复</source>
        <translation>Feed back the problems encountered during use through GitHub issue. We will repair it as soon as possible</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="630"/>
        <source>&amp;编辑快捷键</source>
        <translation>&amp;Edit Shortcuts</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="630"/>
        <source>编辑软件快捷键</source>
        <translation>Edit software shortcuts</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="637"/>
        <source>&amp;调试日志</source>
        <translation>&amp;Debug log</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="637"/>
        <source>用于观察软件执行过程和进行debug。我们不会自动收集任何日志，可能会希望您在反馈问题时间打开此功能，帮助我们定位问题。</source>
        <translation>It is used to observe the software execution process and debug. We don&amp;apos;t automatically collect any logs. We may want you to turn on this function when you feed back the problem to help us locate the problem.</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="646"/>
        <source>&amp;使用QT文件窗口</source>
        <translation>&amp;Use QT file window</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="646"/>
        <source>如果使用文件选择窗口时遇到问题可以选择使用Qt窗口</source>
        <translation>If you encounter problems using the file selection window, you can choose to use the QT window</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="770"/>
        <source>文件</source>
        <translation>File</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="771"/>
        <source>标注</source>
        <translation>Annotation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="772"/>
        <source>功能</source>
        <translation>Functions</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="773"/>
        <source>显示</source>
        <translation>Display</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="774"/>
        <source>帮助</source>
        <translation>Help</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="671"/>
        <source>近期文件</source>
        <translation>Recent documents</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="672"/>
        <source>近期模型及参数</source>
        <translation>Recent models and parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="673"/>
        <source>近期视频传播模型及参数</source>
        <translation>Recent video propagation model and parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="674"/>
        <source>语言</source>
        <translation>Language</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="825"/>
        <source>切换语言</source>
        <translation>Changing language</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="825"/>
        <source>切换语言需要重启软件才能生效</source>
        <translation>Changing language only takes effect after restarting the app</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="841"/>
        <source>无近期文件</source>
        <translation>No recent documents</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="857"/>
        <source>已清除最近打开文件</source>
        <translation>Recently opened files have been cleared</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="877"/>
        <source>无近期模型记录</source>
        <translation>No recent model parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="897"/>
        <source>无近期视频传播模型记录</source>
        <translation>No recent video propagation model record</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="947"/>
        <source>Paddle静态模型权重文件(*.pdiparams)</source>
        <translation>Paddle static model weight files (*.pdiparams)</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="924"/>
        <source>选择传播模型参数</source>
        <translation>Select propagation model parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="966"/>
        <source>参数路径存在中文</source>
        <translation>Parameter path exists in Chinese</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="966"/>
        <source>请修改参数路径为非中文路径！</source>
        <translation>Please change the parameter path to a non Chinese path!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="953"/>
        <source>选择模型参数</source>
        <translation>Select model parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="981"/>
        <source>视频传播模型加载成功</source>
        <translation>Video propagation model loaded successfully</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1003"/>
        <source> 模型加载成功</source>
        <translation> Model loaded successfully</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1017"/>
        <source>掩膜已启用</source>
        <translation>Mask enabled</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1019"/>
        <source>掩膜已关闭</source>
        <translation>Mask closed</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1023"/>
        <source>没有最近使用模型信息，请加载模型</source>
        <translation>No recently used model information, please load the model</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1031"/>
        <source>没有最近使用的视频传播模型信息，请加载模型</source>
        <translation>No recently used video propagation model information, please load the model</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1064"/>
        <source>标签配置文件</source>
        <translation>Label profile</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1045"/>
        <source>选择标签配置文件路径</source>
        <translation>Select label profile path</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1061"/>
        <source>没有需要保存的标签</source>
        <translation>There are no labels to save</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1061"/>
        <source>请先添加标签之后再进行保存！</source>
        <translation>Please add a label before saving!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1065"/>
        <source>保存标签配置文件</source>
        <translation>Save label profile</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1078"/>
        <source>选择保存标签配置文件路径</source>
        <translation>Select the path to save the label profile</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1125"/>
        <source>清空标签列表?</source>
        <translation>Clear label list?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1125"/>
        <source>请确认是否要清空标签列表</source>
        <translation>Please confirm you want to clear the label list</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1197"/>
        <source>无法删除</source>
        <translation>Cannot delete</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1198"/>
        <source>当前多边形中存在此标签</source>
        <translation>This label exists in the current polygon</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1205"/>
        <source>交互式标注模式无法删除标签</source>
        <translation>Interactive annotation mode cannot delete labels</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1241"/>
        <source>确认删除？</source>
        <translation>Confirm deletion?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1241"/>
        <source>确认要删除当前选中多边形标注？</source>
        <translation>Are you sure you want to delete the currently selected polygon dimension?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1310"/>
        <source>选择待标注图片</source>
        <translation>Select the image to be labeled</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1321"/>
        <source>请在gpu电脑上进行视频标注</source>
        <translation>Please mark the video on the GPU computer</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1321"/>
        <source>准备进行视频标注，由于视频标注需要一定计算，请尽量确保在gpu的电脑上进行操作!</source>
        <translation>Prepare for video tagging. Since video tagging requires certain calculation, please try your best to ensure that the operation is carried out on the GPU computer!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1348"/>
        <source>选择待标注图片文件夹</source>
        <translation>Select the picture folder to be labeled</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1435"/>
        <source>未启用医疗组件</source>
        <translation>Medical components are not enabled</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1435"/>
        <source>加载医疗影像需启用医疗组件，是否立即启用？</source>
        <translation>Loading medical images requires enabling medical components. Do you want to enable them now?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1471"/>
        <source>未打开遥感组件</source>
        <translation>Remote sensing component not open</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1471"/>
        <source>打开遥感图像需启用遥感组件，是否立即启用？</source>
        <translation>The remote sensing component needs to be enabled to open the remote sensing image. Do you want to enable it now?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1483"/>
        <source>● 波段数：</source>
        <translation>● Number of bands:</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1483"/>
        <source>● 数据类型：</source>
        <translation>● Data type:</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1483"/>
        <source>● 行数：</source>
        <translation>● Number of rows:</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1483"/>
        <source>● 列数：</source>
        <translation>● Number of columns:</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1510"/>
        <source>无</source>
        <translation>Na</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1515"/>
        <source>未启用视频组件</source>
        <translation>Video component not open</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1515"/>
        <source>加载视频需启用视频组件，是否立即启用？</source>
        <translation>Loading video requires enabling video components. Do you want to enable them now?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1654"/>
        <source>没有后一张图片</source>
        <translation>No next picture</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1656"/>
        <source>没有前一张图片</source>
        <translation>No prev picture</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1671"/>
        <source>模型未加载</source>
        <translation>Model not loaded</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1671"/>
        <source>尚未加载模型，请先加载模型！</source>
        <translation>The model has not been loaded, please load the model first!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1714"/>
        <source>完成最后一个目标？</source>
        <translation>Finish the last goal?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1714"/>
        <source>是否完成最后一个目标的标注，不完成不会进行保存。</source>
        <translation>Whether to complete the annotation of the last target. If not, it will not be saved.</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1735"/>
        <source>保存标签？</source>
        <translation>Save label?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1735"/>
        <source>标签尚未保存，是否保存标签</source>
        <translation>The label has not been saved. Do you want to save the label</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2745"/>
        <source>播放</source>
        <translation>Play</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1801"/>
        <source>文件夹已经存在</source>
        <translation>Folder already exists</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1801"/>
        <source>该文件夹下不为空，您确定继续保存在此路径下吗？</source>
        <translation>This folder is not empty. Are you sure you want to continue saving in this path?</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1848"/>
        <source>视频帧成功保存至</source>
        <translation>Video frames saved to</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1952"/>
        <source>标签成功保存至</source>
        <translation>Label successfully saved to</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1960"/>
        <source>保存标签文件路径</source>
        <translation>Save label file path</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="1979"/>
        <source>选择标签文件保存路径</source>
        <translation>Select the path to save the label file</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2005"/>
        <source>选择标签保存路径</source>
        <translation>Select the folder to save labels</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2108"/>
        <source>未选择模型</source>
        <translation>No model selected</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2108"/>
        <source>尚未选择模型，请先在右上角选择模型</source>
        <translation>No model selected. Please select the model in the upper right corner first</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2115"/>
        <source>未选择当前标签</source>
        <translation>The current label is not selected</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2115"/>
        <source>请先在标签列表中单击点选标签</source>
        <translation>Please click the label in the label list first</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2228"/>
        <source>无法导入GDAL或rasterio</source>
        <translation>Unable to import GDAL or rasterio</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2228"/>
        <source>使用遥感工具需要安装GDAL和rasterio！</source>
        <translation>GDAL and rasterio needs to be installed to use remote sensing tools!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2233"/>
        <source>打开遥感工具失败，请安装GDAL和rasterio</source>
        <translation>Failed to open remote sensing tool. Please install GDAL and rasterio</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2239"/>
        <source>无法导入SimpleITK</source>
        <translation>Cannot import SimpleITK</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2239"/>
        <source>使用医疗工具需要安装SimpleITK！</source>
        <translation>Simpleitk needs to be installed to use medical tools!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2244"/>
        <source>打开医疗工具失败，请安装SimpleITK</source>
        <translation>Failed to open medical tool, please install SimpleITK</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2250"/>
        <source>无法导入VTK</source>
        <translation>Cannot import VTK</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2251"/>
        <source>使用3D显示工具需要安装VTK！</source>
        <translation>VTK needs to be installed to use 3D display tools!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2254"/>
        <source>打开3D显示工具失败，请安装VTK</source>
        <translation>Failed to open 3D display tool, please install VTK</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2493"/>
        <source>图像过大，已显示缩略图</source>
        <translation>The image is too large and thumbnails are displayed</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2578"/>
        <source>图像过大</source>
        <translation>The image is too large</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2578"/>
        <source>图像过大，将启用宫格功能！</source>
        <translation>If the image is too large, the grid function will be enabled!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2629"/>
        <source>功能尚在开发</source>
        <translation>The function is still under development</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2741"/>
        <source>图片格式无法播放</source>
        <translation>Picture format cannot be played</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2741"/>
        <source>请先加载视频</source>
        <translation>Please load the video first</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2750"/>
        <source>暂停</source>
        <translation>Suspend</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2762"/>
        <source>未加载视频</source>
        <translation>Video not loaded</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2762"/>
        <source>请先在加载图像按钮中加载视频</source>
        <translation>Please load the video in the load image button first</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2765"/>
        <source>传播模型未加载</source>
        <translation>Propagation model not loaded</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2765"/>
        <source>尚未加载视频传播模型，请先加载模型!</source>
        <translation>The video propagation model has not been loaded. Please load the model first!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2768"/>
        <source>融合模型未加载</source>
        <translation>Fusion model not loaded</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2768"/>
        <source>尚未加载视频融合模型，请先加载模型!</source>
        <translation>Video fusion model has not been loaded, please load the model first!</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2773"/>
        <source>未提供传播参考帧</source>
        <translation>No propagation reference frame provided</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2773"/>
        <source>请先在标注传播参考帧再进行传播</source>
        <translation>Please propagate the reference frame in the annotation before propagation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2780"/>
        <source>开始传播</source>
        <translation>Start propagation</translation>
    </message>
    <message>
        <location filename="../../eiseg/app.py" line="2796"/>
        <source>传播完成!</source>
        <translation>Propagation completed!</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="57"/>
        <source>编辑快捷键</source>
        <translation>Edit Keyboard Shortcuts</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="90"/>
        <source>-</source>
        <translation>-</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="114"/>
        <source>快捷键冲突</source>
        <translation>Shortcut key conflict</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="115"/>
        <source>快捷键已被</source>
        <translation>shortcut has been used by</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="115"/>
        <source>使用，请设置其他快捷键或先修改</source>
        <translation>. Please set another key sequence or modify the keyboard shotcut of</translation>
    </message>
    <message>
        <location filename="../../eiseg/widget/shortcut.py" line="115"/>
        <source>的快捷键</source>
        <translation>first</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="380"/>
        <source>时间轴</source>
        <translation>Time Axis</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="399"/>
        <source>帧数：</source>
        <translation>Frames:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="130"/>
        <source>模型选择</source>
        <translation>Model Selection</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="141"/>
        <source>加载网络参数</source>
        <translation>Load model parameter</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="149"/>
        <source>使用掩膜</source>
        <translation>Use mask</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="168"/>
        <source>数据列表</source>
        <translation>Image List</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="192"/>
        <source>添加标签</source>
        <translation>Add Label</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="197"/>
        <source>标签列表</source>
        <translation>Label List</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="206"/>
        <source>分割阈值：</source>
        <translation>Segmentation Threshold:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="212"/>
        <source>标签透明度：</source>
        <translation>Label Transparency:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="218"/>
        <source>点击可视化半径：</source>
        <translation>Click Visualization Radius:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="224"/>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="232"/>
        <source>分割设置</source>
        <translation>Segmentation Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="238"/>
        <source>遥感设置</source>
        <translation>Remote sensing Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="241"/>
        <source>波段设置</source>
        <translation>Band setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="254"/>
        <source>保存设置</source>
        <translation>Save settings</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="255"/>
        <source>建筑边界规范化</source>
        <translation>Building boundary normalization</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="258"/>
        <source>另存为shapefile</source>
        <translation>Save extra as shapefile</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="262"/>
        <source>地理信息</source>
        <translation>Geographic information</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="281"/>
        <source>窗宽：</source>
        <translation>Window width:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="286"/>
        <source>窗位：</source>
        <translation>Window level:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="292"/>
        <source>医疗设置</source>
        <translation>Medical Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="297"/>
        <source>宫格切换</source>
        <translation>Palace grid switching</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="306"/>
        <source>完成宫格</source>
        <translation>Complete the grid</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="318"/>
        <source>保存每个宫格的标签</source>
        <translation>Save labels for each grid</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="341"/>
        <source>视频分割设置</source>
        <translation>Video Segmentation Setting</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="344"/>
        <source>加载传播网络参数</source>
        <translation>Load propagation model parameters</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="351"/>
        <source>传播</source>
        <translation>Propagate</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="362"/>
        <source>进度：</source>
        <translation>Progress:</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="370"/>
        <source>3D显示</source>
        <translation>3D Display</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="401"/>
        <source>上一帧</source>
        <translation>Prev frame</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="405"/>
        <source>下一帧</source>
        <translation>Next frame</translation>
    </message>
    <message>
        <location filename="../../eiseg/ui.py" line="415"/>
        <source>倍速: </source>
        <translation>Speed: </translation>
    </message>
</context>
</TS>
