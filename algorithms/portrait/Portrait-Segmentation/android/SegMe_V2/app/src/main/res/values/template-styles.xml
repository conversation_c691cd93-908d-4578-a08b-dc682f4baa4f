<!--
  Copyright 2013 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<resources>

    <!-- Activity themes -->

    <style name="Theme.Base" parent="android:Theme.Light" />

    <style name="Theme.Sample" parent="Theme.Base" />

    <style name="AppTheme" parent="Theme.Sample" />
    <!-- Widget styling -->

    <style name="Widget" />

    <style name="Widget.SampleMessage">
        <item name="android:textAppearance">?android:textAppearanceMedium</item>
        <item name="android:lineSpacingMultiplier">1.1</item>
    </style>

    <style name="Widget.SampleMessageTile">
        <item name="android:background">@drawable/tile</item>
        <item name="android:shadowColor">#7F000000</item>
        <item name="android:shadowDy">-3.5</item>
        <item name="android:shadowRadius">2</item>
    </style>

</resources>
