<?xml version="1.0" encoding="utf-8"?><!--
 Copyright 2014 The Android Open Source Project

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<resources>
    <string name="picture">Picture</string>
    <string name="description_info">Info</string>
    <string name="request_permission">This sample needs camera permission.</string>
    <string name="camera_error">This device doesn\'t support Camera2 API.</string>
    <string name="toggle_turn_on">NN:On</string>
    <string name="toggle_turn_off">NN:Off</string>
    <string name="toggle">Use NNAPI</string>
    <string name="tflite">tflite</string>
    <string name="nnapi">NNAPI</string>
    <string name="gpu">GPU</string>
    <string name="cpu">CPU</string>
    <string name="filterLabel">Filters</string>
    <string name="deviceLabel">Device</string>
    <string name="videoBokeh">Video Bokeh</string>
    <string name="portraitSeg">Alpha Blend</string>
    <string name="colorTrans">Color Transfer</string>
    <string name="renderMerge">Render Merge</string>

</resources>
