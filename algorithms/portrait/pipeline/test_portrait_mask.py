# conda activate paddleseg

import os
import sys

# set path  of BokehMe directory
bokeh_test_folder = "/home/<USER>/T2/data/bokeh_test"
mask_test_output_folder = "/home/<USER>/T2/data/mask_images"
output_test_folder = "/home/<USER>/T2/data/masked_test_output"


# list all tif files in the current directory
files = os.listdir(bokeh_test_folder, )

# loop through all files and print their names
for file in files:
    print(file)
    input_file  = os.path.join(bokeh_test_folder, file)
    output_file = os.path.join(output_test_folder, file)
    mask_file   = os.path.join(mask_test_output_folder, file)
    print("input_file:", input_file)
    print("mask_output_file:", output_file)
    cmd = f"python3 /home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/src/seg_demo.py \
                  --config /home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/inference_models/human_pp_humansegv1_server_512x512_inference_model/deploy.yaml \
                  --img_path {input_file} \
                  --save_dir {output_file} \
                  --save_maskdir {mask_file} "
    
    os.system(cmd)