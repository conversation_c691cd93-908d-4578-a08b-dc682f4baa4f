# conda activate yolofacev2

import os
import sys

# set path  of BokehMe directory
bokeh_test_folder = "/home/<USER>/T2/data/bokeh_test"
facedet_test_output_folder = "/home/<USER>/T2/data/facedetected_images"


# list all tif files in the current directory
files = os.listdir(bokeh_test_folder, )

# loop through all files and print their names
for file in files:
    print(file)
    input_file  = os.path.join(bokeh_test_folder, file)
    mask_file   = os.path.join(facedet_test_output_folder, file)
    print("input_file:", input_file)
    print("mask_output_file:", facedet_test_output_folder)
    cmd = f"python3 /home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/detect.py  \
                --weights /home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/preweight/preweight.pt \
                --source {input_file} \
                --plot-label \
                --save_dir {facedet_test_output_folder}"
    print(cmd)
    os.system(cmd)