batch_size: 64
iters: 2000

train_dataset:
  type: Dataset
  dataset_root: test_tipc/data/mini_supervisely
  train_path: test_tipc/data/mini_supervisely/train.txt
  num_classes: 2
  transforms:
    - type: Resize
      target_size: [192, 192]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.4
      contrast_range: 0.4
      saturation_range: 0.4
    - type: Normalize
  mode: train

val_dataset:
  type: Dataset
  dataset_root: test_tipc/data/mini_supervisely
  val_path: test_tipc/data/mini_supervisely/val.txt
  num_classes: 2
  transforms:
    - type: Resize
      target_size: [192, 192]
    - type: Normalize
  mode: val

export:
  transforms:
    - type: Resize
      target_size: [192, 192]
    - type: Normalize


optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 0.0005

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.05
  end_lr: 0
  power: 0.9

loss:
  types:
    - type: CrossEntropyLoss
  coef: [1]

model:
  type: FCN
  backbone:
    type: HRNet_W18_Small_V1
    align_corners: False
  num_classes: 2
  # pretrained: pretrained_model/fcn_hrnetw18_small_v1_humanseg_192x192/model.pdparams
  backbone_indices: [-1]
