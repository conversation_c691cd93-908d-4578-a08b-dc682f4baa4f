_base_: '../_base_/cityscapes.yml'

batch_size: 8
iters: 120000


optimizer:
  _inherited_: False
  type: adam
  weight_decay: 0.0002

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.001
  end_lr: 0.0
  power: 0.9

loss:
  types:
    - type: CrossEntropyLoss
      weight: [2.79834108 ,6.92945723 ,3.84068512 ,9.94349362 ,9.77098823 ,9.51484 ,10.30981624 ,9.94307377 ,4.64933892 ,9.55759938 ,7.86692178 ,9.53126629 ,10.3496365 ,6.67234062 ,10.26054204 ,10.28785275 ,10.28988296 ,10.40546021 ,10.13848367]
  coef: [1, 1]

model:
  type: ESPNetV2
  in_channels: 3
  scale: 2.0
  num_classes: 19
  drop_prob: 0.0
