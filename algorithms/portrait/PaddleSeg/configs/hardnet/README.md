# HarDNet: A Low Memory Traffic Network

## Reference

> <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. "Hardnet: A low memory traffic network." In Proceedings of the IEEE International Conference on Computer Vision, pp. 3552-3561. 2019.

## Performance

### Cityscapes

| Model | Backbone | Resolution | Training Iters | mIoU | mIoU (flip) | mIoU (ms+flip) | Links |
|-|-|-|-|-|-|-|-|
|HarDNet|-|1024x1024|160000|79.03%|79.49%|79.76%|[model](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/hardnet_cityscapes_1024x1024_160k/model.pdparams) \| [log](https://bj.bcebos.com/paddleseg/dygraph/cityscapes/hardnet_cityscapes_1024x1024_160k/train.log) \| [vdl](https://paddlepaddle.org.cn/paddle/visualdl/service/app?id=b90b16bff783a97baed70313821fe551)|
