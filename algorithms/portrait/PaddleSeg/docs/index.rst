欢迎使用PaddleSeg！
=======================================

PaddleSeg是基于飞桨核心框架的深度学习全流程开发工具。具备 **全流程打通** 、**融合产业实践** 、**易用易集成** 三大特点。
 
* 项目GitHub: https://github.com/PaddlePaddle/PaddleSeg
* 官方QQ用户群: 1045148026  
* GitHub Issue反馈: http://www.github.com/PaddlePaddle/PaddleSeg/issues

1. 注：本使用手册在打印为pdf后，可能会存在部分格式的兼容问题；
2. 注：本文档持续在http://paddleseg.readthedocs.io/进行更新。


.. toctree::
   :maxdepth: 1
   :caption: 1. 快速了解PaddleSeg
   
   install.md
   quick_start.md
   

.. toctree::
   :maxdepth: 1
   :caption: 2. 数据处理
   
   data/marker/index.rst
   data/transform/index.rst
   data/custom/index.rst

.. toctree::
   :maxdepth: 1
   :caption: 3. PaddleSeg设计思想
   
   design/use/index.rst
   design/create/index.rst


.. toctree::
   :maxdepth: 1
   :caption: 4. 模型训练
   
   train/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 5. 模型评估
   
   evaluation/evaluate/index.rst
   

.. toctree::
   :maxdepth: 1
   :caption: 6. 模型导出
   
   export/export/index.rst


.. toctree::
   :maxdepth: 1
   :caption: 7. 模型部署
   
   deployment/inference/index.rst
   deployment/serving/index.rst
   deployment/lite/index.rst
   deployment/web/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 8. API使用教程
   
   api_example.md
   apis/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 9. 模型压缩
   
   slim/quant/index.rst
   slim/prune/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 10. 重要模块说明
   
   module/data/index.rst
   module/loss/index.rst
   module/tricks/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 11. 经典模型说明
   
   models/unet.md
   models/deeplabv3.md
   models/ocrnet.md
   models/fascnn.md
   
.. toctree::
   :maxdepth: 1
   :caption: 12. 产品实践解决方案
   
   solution/human/index.rst
   solution/remotesensing/index.rst
   solution/medical/index.rst

.. toctree::
   :maxdepth: 1
   :caption: 13. 提交PR说明
   
   pr/pr/index.rst
   
.. toctree::
   :maxdepth: 1
   :caption: 14. FAQ文档问答
   
   faq/faq/index.rst

