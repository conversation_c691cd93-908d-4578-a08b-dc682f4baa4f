_base_: '../_base_/cityscapes.yml'

batch_size: 2
iters: 80000

model:
  type: ENCNet
  backbone:
    type: ResNet101_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet101_vd_ssld.tar.gz
  num_codes: 32
  mid_channels: 512
  backbone_indices: [1, 2, 3]
  use_se_loss: True
  add_lateral: True

train_dataset:
  type: Cityscapes
  dataset_root: test_tipc/data/cityscapes
  transforms:
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0.25
    - type: RandomPaddingCrop
      crop_size: [512, 512]  # let gpu mem < ci machine gpu mem=15g
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.4
      contrast_range: 0.4
      saturation_range: 0.4
    - type: Normalize
  mode: train

val_dataset:
  type: Cityscapes
  dataset_root: test_tipc/data/cityscapes
  transforms:
    - type: Resize
      target_size: [512, 512] # let gpu mem < ci machine gpu mem=15g
    - type: Normalize
  mode: val

optimizer:
  type: sgd
  weight_decay: 0.0005

loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
    - type: SECrossEntropyLoss
  coef: [1, 0.4, 0.2]

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  end_lr: 0.0
  power: 0.9
