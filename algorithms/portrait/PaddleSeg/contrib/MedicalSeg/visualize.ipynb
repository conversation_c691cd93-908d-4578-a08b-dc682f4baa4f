{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Please install the itkwidgts before experiment\n", "details to install it can be found [here](https://github.com/InsightSoftwareConsortium/itkwidgets#installation)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: scipy in /ssd2/tangshiyu/anaconda3/lib/python3.8/site-packages (1.6.2)\r\n", "Requirement already satisfied: numpy<1.23.0,>=1.16.5 in /ssd2/tangshiyu/anaconda3/lib/python3.8/site-packages (from scipy) (1.20.1)\r\n"]}], "source": ["# Install dependencies for this example\n", "# Note: This does not include itkwidgets, itself\n", "import sys\n", "!{sys.executable} -m pip install scipy\n", "!wget https://bj.bcebos.com/paddleseg/paddleseg3d/lung_coronavirus/vnet_lung_coronavirus_128_128_128_15k_1e-3/prediction.npz"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from itkwidgets import view, compare\n", "\n", "# load the compressed data img=a, label=b, pred=c, infer=d\n", "data = np.load(\"prediction.npz\")\n", "\n", "# the infer output \n", "infer_result = data['infer'].squeeze()\n", "print(infer_result.shape)\n", "infer_result = infer_result*100\n", "\n", "# Ascent has a range of values from 0 to 255, but it is stored with a int64 dtype\n", "infer_result = infer_result.astype(np.uint8)\n", "\n", "b = data['pred'].squeeze()\n", "print(b.shape)\n", "b = b*100\n", "\n", "# Ascent has a range of values from 0 to 255, but it is stored with a int64 dtype\n", "b = b.astype(np.uint8)\n", "\n", "c = data['label'].squeeze()\n", "print(b.shape)\n", "c = c*100\n", "\n", "# Ascent has a range of values from 0 to 255, but it is stored with a int64 dtype\n", "c = c.astype(np.uint8)\n", "\n", "d = data['img'].squeeze()\n", "print(d.shape)\n", "d = d*100\n", "\n", "# Ascent has a range of values from 0 to 255, but it is stored with a int64 dtype\n", "d = d.astype(np.uint8)\n", "\n", "# b is pred, c is label, d is img\n", "predlabel = np.concatenate((b, c), axis=1)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(128, 256, 128)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "762485dcc73340b0a2b23c6eee6eaf3f", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(axes=True, geometries=[], gradient_opacity=1.0, point_sets=[], rendered_image=<itk.itkImagePython.itkIm…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(predlabel.shape)\n", "\n", "view(predlabel[:, 50:240, :],  axes=True, vmin=0, vmax=200, gradient_opacity=1.9)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2412dd34d0cf4e568b6ca1fe628485e6", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(axes=True, cmap=['OrPu', 'BkGn'], geometries=[], gradient_opacity=1.0, interpolation=False, point_sets=…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["view(label_image=predlabel[:, 50:240, :], rotate=True, axes=True, vmin=0, vmax=200, gradient_opacity=10.0, cmap=[\"OrPu\", 'BkGn'])"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2168100f87274a93b1c1b2c331f06cb7", "version_major": 2, "version_minor": 0}, "text/plain": ["AppLayout(children=(HBox(children=(Label(value='Link:'), Checkbox(value=True, description='cmap'), Checkbox(va…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["compare(b, c, link_cmap=True, rotate=True)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "all the input arrays must have same number of dimensions, but the array at index 0 has 3 dimension(s) and the array at index 1 has 4 dimension(s)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-39-c5383c193e35>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 15\u001b[0;31m \u001b[0mpredlabel\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mconcatenate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmri_pred\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmri_label\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     16\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[0mpredlabel\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<__array_function__ internals>\u001b[0m in \u001b[0;36mconcatenate\u001b[0;34m(*args, **kwargs)\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: all the input arrays must have same number of dimensions, but the array at index 0 has 3 dimension(s) and the array at index 1 has 4 dimension(s)"]}], "source": ["#  visualize  MRI\n", "import numpy as np\n", "from itkwidgets import view, compare\n", "\n", "mri_img = np.load(\"saved_model/vnet_mri_spine_seg_128_128_24_15k_0309_rmmax/1_img.npy\")\n", "mri_label = np.load(\"saved_model/vnet_mri_spine_seg_128_128_24_15k_0309_rmmax/1_label.npy\") * 10\n", "mri_pred = np.load(\" saved_model/vnet_mri_spine_seg_128_128_24_15k_0309_rmmax/1_pred.npy\") * 10\n", "\n", "# Ascent has a range of values from 0 to 255, but it is stored with a int64 dtype\n", "mri_img = mri_img.astype(np.uint8)\n", "mri_label = mri_label.astype(np.uint8)\n", "mri_pred = mri_pred.astype(np.uint8)\n", "\n", "\n", "predlabel = np.concatenate((mri_pred, mri_label), axis=2)\n", "\n", "predlabel.shape"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6cf8328d4cdd4f4293c10ea116d261be", "version_major": 2, "version_minor": 0}, "text/plain": ["Viewer(geometries=[], gradient_opacity=1.0, point_sets=[], rendered_image=<itk.itkImagePython.itkImageUC3; pro…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["view(mri_pred, axes=False, rotate=True, vmin=0, vmax=200, gradient_opacity=190)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}