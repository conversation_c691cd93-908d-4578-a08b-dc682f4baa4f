batch_size: 4
iters: 50000

train_dataset:
  type: MattingDataset
  dataset_root: data/PPM-100
  train_file: train.txt
  transforms:
    - type: LoadImages
    - type: RandomResize
      size: [2048, 2048]
      scale: [0.3, 1.5]
    - type: RandomCrop
      crop_size: [2048, 2048]
    - type: RandomDistort
    - type: RandomBlur
      prob: 0.1
    - type: RandomHorizontalFlip
    - type: Padding
      target_size: [2048, 2048]
    - type: Normalize
  mode: train

val_dataset:
  type: MattingDataset
  dataset_root: data/PPM-100
  val_file: val.txt
  transforms:
    - type: LoadImages
    - type: ResizeByShort
      short_size: 2048
    - type: ResizeToIntMult
      mult_int: 128
    - type: Normalize
  mode: val
  get_trimap: False

model:
  type: HumanMatting
  backbone:
    type: ResNet34_vd
    pretrained: https://paddleseg.bj.bcebos.com/matting/models/ResNet34_vd_pretrained/model.pdparams
  pretrained: Null
  if_refine: True

optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 4.0e-5

lr_scheduler:
  type: PiecewiseDecay
  boundaries: [30000, 40000]
  values: [0.001, 0.0001, 0.00001]
