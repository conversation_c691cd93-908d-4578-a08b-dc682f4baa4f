{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "ONNX_Conversion.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "GNk19sOXsEBg", "colab_type": "text"}, "source": ["**Converting Segmentation Models into ONNX Format**"]}, {"cell_type": "markdown", "metadata": {"id": "7kOkR85ttJEP", "colab_type": "text"}, "source": ["Th nvidia **deepstream** sdk currently supports model formats like **caffe, onnx and uff** only. So we need to convert the **keras and tensorflow** models to **onnx nchw** format for deployment."]}, {"cell_type": "code", "metadata": {"id": "tgLI90Mki8Ff", "colab_type": "code", "colab": {}}, "source": ["# Install tensorflow-gpu 1.15 \n", "!pip install tensorflow-gpu==1.15"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "deKlmdnSi97B", "colab_type": "text"}, "source": ["**Keras to ONNX: Prisma-net**"]}, {"cell_type": "markdown", "metadata": {"id": "xksCNrWnr37R", "colab_type": "text"}, "source": ["Install **keras2onnx** package for converting the **prisma-net keras model to onnx** format."]}, {"cell_type": "code", "metadata": {"id": "IgzIQEkPFcDU", "colab_type": "code", "colab": {}}, "source": ["!pip install git+https://github.com/microsoft/onnxconverter-common\n", "!pip install git+https://github.com/onnx/keras-onnx"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Bfdc9mKEH7FK", "colab_type": "code", "colab": {}}, "source": ["from tensorflow.keras.models import load_model\n", "import tensorflow as tf\n", "from tensorflow.keras.layers import Activation, Lambda, Reshape, Permute\n", "from tensorflow.keras.models import Model"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "CNgGu_JLOMfx", "colab_type": "code", "colab": {}}, "source": ["import onnx, os\n", "os.environ['TF_KERAS'] = '1' # USe tf.keras backend\n", "import numpy as np\n", "import keras2onnx"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "t5Fp8O63IZQi", "colab_type": "code", "colab": {}}, "source": ["def bilinear_resize(x, rsize):\n", "  return tf.image.resize_bilinear(x, [rsize,rsize], align_corners=True)"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "W_kdH1QXs5wn", "colab_type": "text"}, "source": ["Load the keras **prisma-net** model"]}, {"cell_type": "code", "metadata": {"id": "nAo5aSo-ICfc", "colab_type": "code", "colab": {}}, "source": ["prisma_model=load_model('/content/prisma-net-15-0.08.hdf5')"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hRZD5lEXtNXQ", "colab_type": "text"}, "source": ["Add a **permute** laye for converting **output** into **NCHW** format."]}, {"cell_type": "code", "metadata": {"id": "dZGRCKyYpsyf", "colab_type": "code", "colab": {}}, "source": ["nchw=Permute((3,1,2))(prisma_model.output)\n", "nchw_model=Model(inputs=prisma_model.input, outputs=nchw)\n", "nchw_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "l1e4j8f6tm16", "colab_type": "text"}, "source": ["Convert the keras model to **onnx** format"]}, {"cell_type": "code", "metadata": {"id": "e4iWAx7GaRCT", "colab_type": "code", "colab": {}}, "source": ["onnx_prisma = keras2onnx.convert_keras(nchw_model,'prisma_nchw', channel_first_inputs=['input_3'], target_opset=7)\n", "keras2onnx.save_model(onnx_prisma, 'prisma_nchw.onnx')\n", "onnx.checker.check_model(onnx_prisma)"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "WqTsqaF2mCpX", "colab_type": "text"}, "source": ["**Note:** Keep the **target opset** to **minimum** value, so that it runs with the IR version of **inference engine** parser."]}, {"cell_type": "markdown", "metadata": {"id": "24Wi6-Y4gxCf", "colab_type": "text"}, "source": ["**Tensorflow to ONNX: DeeplabV3+**"]}, {"cell_type": "markdown", "metadata": {"id": "5_Wbw9wXrhbU", "colab_type": "text"}, "source": ["Install **tf2onnx** package for converting the **deeplab tensorflow frozen model to onnx** format."]}, {"cell_type": "code", "metadata": {"id": "5Yr6yil5g4EE", "colab_type": "code", "colab": {}}, "source": ["!pip install -U tf2onnx"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GolS70zRuHLh", "colab_type": "text"}, "source": ["Add a **permute** layer  to get the output in **NCHW** format"]}, {"cell_type": "code", "metadata": {"id": "vTc7O1Lyg_fc", "colab_type": "code", "colab": {}}, "source": ["import numpy as np\n", "import tensorflow as tf\n", "from tensorflow.python.platform import gfile\n", "\n", "GRAPH_PB_PATH = '/content/transform_deeplab_graph_fin4.pb'\n", "with tf.Session() as sess:\n", "\n", "   print(\"Loading graph...\")\n", "   with gfile.FastGFile(GRAPH_PB_PATH,'rb') as f:\n", "       graph_def = tf.GraphDef()\n", "   graph_def.ParseFromString(f.read())\n", "   sess.graph.as_default()\n", "   tf.import_graph_def(graph_def, name='')\n", "   \n", "   # Add transpose layer for nchw output\n", "   output2 = tf.transpose(tf.get_default_graph().get_tensor_by_name(\"ResizeBilinear_2:0\"), perm=[0,3,1,2])\n", "\n", "   print(\"Writing graph...\")\n", "   tf.train.write_graph(tf.get_default_graph().as_graph_def(), 'deeplab_nchw_frozen','deeplab_nchw.pb',as_text=False)"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "dk-Q9GKzuVaJ", "colab_type": "text"}, "source": ["**Convert** the keras model to **onnx** format"]}, {"cell_type": "code", "metadata": {"id": "pAQea4XDhHN-", "colab_type": "code", "colab": {}}, "source": ["!python -m tf2onnx.convert --graphdef /content/deeplab_nchw_frozen/deeplab_nchw.pb --output deeplab_channel.onnx --inputs MobilenetV2/MobilenetV2/input:0 --inputs-as-nchw MobilenetV2/MobilenetV2/input:0 --outputs transpose:0"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "bhLaak2jk0Aw", "colab_type": "text"}, "source": ["**Optimize** the onnx model by **folding batch norm** layers"]}, {"cell_type": "code", "metadata": {"id": "6lVK2evTvoDT", "colab_type": "code", "colab": {}}, "source": ["import onnx\n", "from onnx import optimizer\n", "\n", "# Load the model to be optimized.\n", "model_path = '/content/deeplab_channel.onnx'\n", "original_model = onnx.load(model_path)\n", "\n", "# Fuse the batchnorm layers into conv\n", "optim_passes = ['fuse_bn_into_conv']\n", "\n", "# Apply the optimization on the original model\n", "optimized_deeplab = optimizer.optimize(original_model, optim_passes)\n", "onnx.checker.check_model(optimized_deeplab)\n", "onnx.save(optimized_deeplab, '/content/deeplab_nchw.onnx')"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "3mrhjtd3nKSb", "colab_type": "text"}, "source": ["**Note:** Ensure that the **inputs are in nchw** format during conversion by using appropriate commnand-line arguments eg: **inputs-as-nchw**. Now for outputs, add a **permute** layer to convert them to NCHW format. These steps helps us to **prevent** the addition of **redundant transpose** layers in the converted model. Now, we can run these onnx model directly using the **nvidia deepstream** inference engine."]}]}