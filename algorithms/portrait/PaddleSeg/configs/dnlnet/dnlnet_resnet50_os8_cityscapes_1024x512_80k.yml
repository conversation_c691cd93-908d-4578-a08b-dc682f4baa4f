_base_: '../_base_/cityscapes.yml'

batch_size: 2
iters: 80000

model:
  type: DNLNet
  backbone:
    type: ResNet50_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet50_vd_ssld_v2.tar.gz
  num_classes: 19

optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 0.00004

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  power: 0.9


loss:
  types:
    - type: CrossEntropyLoss
    - type: CrossEntropyLoss
  coef: [1, 0.4]
