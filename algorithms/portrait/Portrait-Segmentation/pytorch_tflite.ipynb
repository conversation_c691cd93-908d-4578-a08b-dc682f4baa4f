{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "crazy_conversion.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "c958DqEXVmzy", "colab_type": "text"}, "source": ["**Pytorch to Tflite via Keras**"]}, {"cell_type": "markdown", "metadata": {"id": "r4KAwE3RNyg5", "colab_type": "text"}, "source": ["First upload the '**model_mobilenetv2_seg_small.py**' file from portrait-net repo into colab root folder i.e **'/content'**. It contains the **architecture** of the model. Now upload the **weights** of the trained model i.e '**pnet_video.pth**' into colab."]}, {"cell_type": "code", "metadata": {"id": "KZYhma88PBot", "colab_type": "code", "colab": {}}, "source": ["import torch\n", "import torchvision\n", "import model_mobilenetv2_seg_small\n", "\n", "dummy_input = torch.randn(1, 4, 224, 224).cuda()\n", "model =  torch.load('/content/pnet_video.pth').cuda()\n", "\n", "torch.onnx.export(model, dummy_input, \"pnet_video.onnx\", verbose=True)"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "xtPqG_MhWFXh", "colab_type": "text"}, "source": ["**Note:** Providing input and output names sets the display names for values within the model's graph. Setting these does not change the semantics of the graph; it is only for readability.\n", "\n", "The inputs to the network consist of the flat list of inputs (i.e. the values you would pass to the forward() method) followed by the flat list of parameters. You can partially specify names, i.e. provide a list here shorter than the number of inputs to the model, and we will only set that subset of names, starting from the beginning.\n", "\n", "You may also use the **entire saved model**, including model architecture and weights, as input to the converter."]}, {"cell_type": "markdown", "metadata": {"id": "AohjLYC3VwNJ", "colab_type": "text"}, "source": ["**ONNX to Keras**"]}, {"cell_type": "markdown", "metadata": {"id": "rS36LmBIO9sd", "colab_type": "text"}, "source": ["Install latest version of **tensorflow, onnx and onnx2keras**."]}, {"cell_type": "code", "metadata": {"id": "ZoanqmhsZE-L", "colab_type": "code", "colab": {}}, "source": ["!pip install tensorflow-gpu\n", "!pip install onnx\n", "!git clone https://github.com/nerox8664/onnx2keras.git"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "fBdXsxhKPIok", "colab_type": "text"}, "source": ["Change directory to **onnx2keras** root folder."]}, {"cell_type": "code", "metadata": {"id": "jxlRRfX2eQO7", "colab_type": "code", "colab": {}}, "source": ["%cd onnx2keras"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "KyOHAFgKPT9d", "colab_type": "text"}, "source": ["Load the onnx model and convert it to keras model with **onnx2keras**. Ensure that the parameter **change_ordering** is True, for changing the channel format from **NCHW to NHWC**."]}, {"cell_type": "code", "metadata": {"id": "yJfCAB9qeTa-", "colab_type": "code", "colab": {}}, "source": ["import tensorflow as tf\n", "import onnx\n", "from onnx2keras import onnx_to_keras\n", "from tensorflow.keras.models import load_model\n", "\n", "# Load ONNX model\n", "onnx_model = onnx.load('/content/pnet_video.onnx')\n", "\n", "# Call the converter and save keras model\n", "k_model = onnx_to_keras(onnx_model, ['input.1'],change_ordering=True)\n", "k_model.save('/content/pnet_video.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "YAjmr9FqUyH0", "colab_type": "text"}, "source": ["**NB:** It may take about a minute for the conversion process to complete. "]}, {"cell_type": "markdown", "metadata": {"id": "tS2xqICvw1P5", "colab_type": "text"}, "source": ["**Keras Model Modification**"]}, {"cell_type": "markdown", "metadata": {"id": "-HbPcFVcQA-m", "colab_type": "text"}, "source": ["Our model contains **two outputs** corresponding to mask and edge. We need to **remove** the **edge output** from the model, since it is not need during model inference."]}, {"cell_type": "code", "metadata": {"id": "o2LWNmRv2C7-", "colab_type": "code", "colab": {}}, "source": ["from tensorflow.keras.models import Model\n", "from tensorflow.keras.models import load_model\n", "from tensorflow.keras.layers import Activation, Lambda, Reshape"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "3EgJJ4shw6Ur", "colab_type": "code", "colab": {}}, "source": ["# Load keras model\n", "k_model=load_model('/content/pnet_video.h5')\n", "k_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "code", "metadata": {"id": "GI0kL_lnze9G", "colab_type": "code", "colab": {}}, "source": ["# Remove edge branch from output\n", "edge_model=Model(inputs=k_model.input,outputs=k_model.layers[-2].output)\n", "edge_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "AIrsbqpZQf9T", "colab_type": "text"}, "source": ["Now our model has one **two channel** output corresponding to foreground and output. First add a **softmax layer** at the end of the model(over channel axis) to restrict  the output range between 0 and 1."]}, {"cell_type": "code", "metadata": {"id": "vuBVzkMB6Kg8", "colab_type": "code", "colab": {}}, "source": ["# Add softmax on output\n", "sm=Lambda(lambda x: tf.nn.softmax(x))(edge_model.output)\n", "soft_model=Model(inputs=edge_model.input, outputs=sm)\n", "soft_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ezIOVpPpRKf-", "colab_type": "text"}, "source": ["Now, let's get the softmax slice for the **foreground** channel, using **strided slice**."]}, {"cell_type": "code", "metadata": {"id": "xWFagTUmEJ3E", "colab_type": "code", "colab": {}}, "source": ["# Get foreground softmax slice\n", "ip = soft_model.output\n", "str_slice=Lambda(lambda x: tf.strided_slice(x, [0,0, 0, 1], [1,224, 224, 2], [1, 1, 1, 1]))(ip)\n", "stride_model=Model(inputs=soft_model.input, outputs=str_slice)\n", "stride_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ngJ-CFKlRmC6", "colab_type": "text"}, "source": ["Finally, **flatten** the output to 1D i.e 1x224x22x1 => 1x50176"]}, {"cell_type": "code", "metadata": {"id": "pVDtC_QH1W6h", "colab_type": "code", "colab": {}}, "source": ["# Flatten output\n", "output = stride_model.output\n", "newout=Reshape((50176,))(output)\n", "reshape_model=Model(stride_model.input,newout)\n", "reshape_model.summary()"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "V3WNG-AaSFg0", "colab_type": "text"}, "source": ["Save the **final keras** model."]}, {"cell_type": "code", "metadata": {"id": "jTbJsrQL68pW", "colab_type": "code", "colab": {}}, "source": ["# Save keras model\n", "reshape_model.save('/content/portrait_video.h5')"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "yVFBGighNfUp", "colab_type": "text"}, "source": ["**Keras to Tflite**"]}, {"cell_type": "markdown", "metadata": {"id": "Bi483W_jSRip", "colab_type": "text"}, "source": ["Finally, convert the keras model to tflite using tensorflow **tflite-converter**."]}, {"cell_type": "code", "metadata": {"id": "u7WwjJzgHPKi", "colab_type": "code", "colab": {}}, "source": ["# Convert to tflite\n", "import tensorflow as tf\n", "\n", "converter = tf.lite.TFLiteConverter.from_keras_model(reshape_model)\n", "tflite_model = converter.convert()\n", "open(\"/content/portrait_video.tflite\", \"wb\").write(tflite_model)"], "execution_count": 0, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "zwMUrRiFSl7P", "colab_type": "text"}, "source": ["**NB:** For verification or model inspection use [Netron](https://lutzroeder.github.io/netron/) web-app."]}]}