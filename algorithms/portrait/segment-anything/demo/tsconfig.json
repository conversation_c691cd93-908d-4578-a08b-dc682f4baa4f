{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react", "incremental": true, "target": "ESNext", "useDefineForClassFields": true, "allowSyntheticDefaultImports": true, "outDir": "./dist/", "sourceMap": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src"], "exclude": ["node_modules"]}