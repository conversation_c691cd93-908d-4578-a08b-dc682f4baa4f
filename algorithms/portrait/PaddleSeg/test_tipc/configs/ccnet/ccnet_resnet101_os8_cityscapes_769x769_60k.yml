_base_: '../_base_/cityscapes_769x769.yml'

batch_size: 2
iters: 60000

model:
  type: CCNet
  backbone:
    type: ResNet101_vd
    output_stride: 8
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet101_vd_ssld.tar.gz
  backbone_indices: [2, 3]
  enable_auxiliary_loss: True
  dropout_prob: 0.1
  recurrence: 2
  
loss:
  types:
    - type: OhemCrossEntropyLoss
    - type: CrossEntropyLoss
  coef: [1, 0.4]
  
lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  power: 0.9
  end_lr: 1.0e-4
