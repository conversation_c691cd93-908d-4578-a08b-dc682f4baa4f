_base_: '../_base_/cityscapes.yml'

batch_size: 4
iters: 80000

optimizer:
  weight_decay: 5.0e-4

lr_scheduler:
  warmup_iters: 1000
  warmup_start_lr: 1.0e-5
  learning_rate: 0.005

loss:
  types:
    - type: OhemCrossEntropyLoss
      min_kept: 130000
  coef: [1]

train_dataset:
  transforms:
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0.25
    - type: RandomPaddingCrop
      crop_size: [1024, 512]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.5
      contrast_range: 0.5
      saturation_range: 0.5
    - type: Normalize
  mode: train

model:
  type: LRASPP
  backbone:
    type: MobileNetV3_large_x1_0_os8  # out channels: [24, 40, 112, 160]
    pretrained: https://paddleseg.bj.bcebos.com/dygraph/backbone/mobilenetv3_large_x1_0_ssld.tar.gz
  backbone_indices: [0, 1, 3]
  lraspp_head_inter_chs: [32, 64]
  lraspp_head_out_ch: 128
  resize_mode: bilinear