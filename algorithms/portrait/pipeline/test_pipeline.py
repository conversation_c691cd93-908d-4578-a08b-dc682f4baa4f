#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Portrait Pipeline
"""

import os
import sys
import time
import logging
import argparse

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_pipeline.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("Test Pipeline")

def main():
    """测试Portrait Pipeline"""
    parser = argparse.ArgumentParser(description="测试Portrait Pipeline")
    parser.add_argument("--input_dir", type=str, default="/home/<USER>/T2/data/bokeh_test",
                        help="输入图像目录")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/T2/data/pipeline_output",
                        help="输出根目录")
    parser.add_argument("--single_image", action="store_true",
                        help="只处理第一张图像（用于测试）")
    args = parser.parse_args()

    logger.info("="*50)
    logger.info("开始测试Portrait Pipeline")
    logger.info("="*50)
    
    # 运行pipeline.py
    cmd = f"python3 pipeline.py --input_dir {args.input_dir} --output_dir {args.output_dir}"
    if args.single_image:
        cmd += " --single_image"
    
    logger.info(f"执行命令: {cmd}")
    start_time = time.time()
    os.system(cmd)
    end_time = time.time()
    
    logger.info(f"测试完成，总耗时: {end_time - start_time:.2f}秒")
    logger.info("="*50)

if __name__ == "__main__":
    main()
