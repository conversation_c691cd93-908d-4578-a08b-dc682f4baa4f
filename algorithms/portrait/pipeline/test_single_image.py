#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单张图像的处理流程
"""

import os
import sys
import time
import logging
import cv2
import numpy as np
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("Single Image Test")

# 设置输入和输出路径
INPUT_DIR = "/home/<USER>/T2/data/bokeh_test"
OUTPUT_ROOT = "/home/<USER>/T2/data/pipeline_output"
FACE_DETECTION_DIR = os.path.join(OUTPUT_ROOT, "face_detection")
PORTRAIT_MASK_DIR = os.path.join(OUTPUT_ROOT, "portrait_mask")
DEPTH_DIR = os.path.join(OUTPUT_ROOT, "depth")
FACE_MASK_DIR = os.path.join(OUTPUT_ROOT, "face_mask")
BOKEH_OUTPUT_DIR = os.path.join(OUTPUT_ROOT, "bokeh_output")

# 创建输出目录
os.makedirs(OUTPUT_ROOT, exist_ok=True)
os.makedirs(FACE_DETECTION_DIR, exist_ok=True)
os.makedirs(PORTRAIT_MASK_DIR, exist_ok=True)
os.makedirs(DEPTH_DIR, exist_ok=True)
os.makedirs(FACE_MASK_DIR, exist_ok=True)
os.makedirs(BOKEH_OUTPUT_DIR, exist_ok=True)

# 设置模型路径
YOLO_FACE_MODEL = "/home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/preweight/preweight.pt"
PORTRAIT_SEG_MODEL = "/home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/inference_models/human_pp_humansegv1_server_512x512_inference_model/deploy.yaml"

def main():
    """测试单张图像的处理流程"""
    logger.info("="*50)
    logger.info("开始测试单张图像处理")
    logger.info("="*50)
    
    # 选择第一张图像进行处理
    image_files = os.listdir(INPUT_DIR)
    if not image_files:
        logger.error(f"未在 {INPUT_DIR} 目录下找到任何图像")
        return
    
    # 选择第一张jpg图像
    jpg_files = [f for f in image_files if f.lower().endswith('.jpg')]
    if not jpg_files:
        logger.error(f"未在 {INPUT_DIR} 目录下找到任何jpg图像")
        return
    
    image_file = jpg_files[0]
    image_path = os.path.join(INPUT_DIR, image_file)
    logger.info(f"选择图像: {image_path}")
    
    # 1. 人脸检测
    logger.info("步骤1: 人脸检测")
    cmd = f"python3 /home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/detect.py \
            --weights {YOLO_FACE_MODEL} \
            --source {image_path} \
            --save-txt \
            --save_dir {FACE_DETECTION_DIR}"
    logger.info(f"执行命令: {cmd}")
    os.system(cmd)
    
    # 2. 人像分割
    logger.info("步骤2: 人像分割")
    mask_file = os.path.join(PORTRAIT_MASK_DIR, "mask_" + image_file)
    output_file = os.path.join(PORTRAIT_MASK_DIR, image_file)
    cmd = f"python3 /home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/src/seg_demo.py \
            --config {PORTRAIT_SEG_MODEL} \
            --img_path {image_path} \
            --save_dir {output_file} \
            --save_maskdir {mask_file}"
    logger.info(f"执行命令: {cmd}")
    os.system(cmd)
    
    # 3. 深度计算
    logger.info("步骤3: 深度估计")
    cmd = f"python3 /home/<USER>/code/IEDemo/algorithms/portrait/Depth-Anything-V2/run_depth.py \
            --encoder vitl \
            --img-path {image_path} \
            --outdir {DEPTH_DIR}"
    logger.info(f"执行命令: {cmd}")
    os.system(cmd)
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
