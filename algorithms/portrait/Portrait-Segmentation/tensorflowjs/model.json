{"format": "layers-model", "generatedBy": "keras v2.2.4", "convertedBy": "TensorFlow.js Converter v1.2.6", "modelTopology": {"keras_version": "2.2.4", "backend": "tensorflow", "model_config": {"class_name": "Model", "config": {"name": "model_1", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 128, 128, 3], "dtype": "float32", "sparse": false, "name": "input_1"}, "inbound_nodes": []}, {"name": "Conv1_pad", "class_name": "ZeroPadding2D", "config": {"name": "Conv1_pad", "trainable": true, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "inbound_nodes": [[["input_1", 0, 0, {}]]]}, {"name": "bn_Conv1", "class_name": "Conv2D", "config": {"name": "bn_Conv1", "trainable": true, "filters": 16, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["Conv1_pad", 0, 0, {}]]]}, {"name": "Conv1_relu", "class_name": "ReLU", "config": {"name": "Conv1_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["bn_Conv1", 0, 0, {}]]]}, {"name": "expanded_conv_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "expanded_conv_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["Conv1_relu", 0, 0, {}]]]}, {"name": "expanded_conv_depthwise_relu", "class_name": "ReLU", "config": {"name": "expanded_conv_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["expanded_conv_depthwise_BN", 0, 0, {}]]]}, {"name": "expanded_conv_project_BN", "class_name": "Conv2D", "config": {"name": "expanded_conv_project_BN", "trainable": true, "filters": 8, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["expanded_conv_depthwise_relu", 0, 0, {}]]]}, {"name": "block_1_expand_BN", "class_name": "Conv2D", "config": {"name": "block_1_expand_BN", "trainable": true, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["expanded_conv_project_BN", 0, 0, {}]]]}, {"name": "block_1_expand_relu", "class_name": "ReLU", "config": {"name": "block_1_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_1_expand_BN", 0, 0, {}]]]}, {"name": "block_1_pad", "class_name": "ZeroPadding2D", "config": {"name": "block_1_pad", "trainable": true, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "inbound_nodes": [[["block_1_expand_relu", 0, 0, {}]]]}, {"name": "block_1_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_1_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_1_pad", 0, 0, {}]]]}, {"name": "block_1_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_1_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_1_depthwise_BN", 0, 0, {}]]]}, {"name": "block_1_project_BN", "class_name": "Conv2D", "config": {"name": "block_1_project_BN", "trainable": true, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_1_depthwise_relu", 0, 0, {}]]]}, {"name": "block_2_expand_BN", "class_name": "Conv2D", "config": {"name": "block_2_expand_BN", "trainable": true, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_1_project_BN", 0, 0, {}]]]}, {"name": "block_2_expand_relu", "class_name": "ReLU", "config": {"name": "block_2_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_2_expand_BN", 0, 0, {}]]]}, {"name": "block_2_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_2_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_2_expand_relu", 0, 0, {}]]]}, {"name": "block_2_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_2_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_2_depthwise_BN", 0, 0, {}]]]}, {"name": "block_2_project_BN", "class_name": "Conv2D", "config": {"name": "block_2_project_BN", "trainable": true, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_2_depthwise_relu", 0, 0, {}]]]}, {"name": "block_2_add", "class_name": "Add", "config": {"name": "block_2_add", "trainable": true}, "inbound_nodes": [[["block_1_project_BN", 0, 0, {}], ["block_2_project_BN", 0, 0, {}]]]}, {"name": "block_3_expand_BN", "class_name": "Conv2D", "config": {"name": "block_3_expand_BN", "trainable": true, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_2_add", 0, 0, {}]]]}, {"name": "block_3_expand_relu", "class_name": "ReLU", "config": {"name": "block_3_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_3_expand_BN", 0, 0, {}]]]}, {"name": "block_3_pad", "class_name": "ZeroPadding2D", "config": {"name": "block_3_pad", "trainable": true, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "inbound_nodes": [[["block_3_expand_relu", 0, 0, {}]]]}, {"name": "block_3_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_3_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_3_pad", 0, 0, {}]]]}, {"name": "block_3_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_3_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_3_depthwise_BN", 0, 0, {}]]]}, {"name": "block_3_project_BN", "class_name": "Conv2D", "config": {"name": "block_3_project_BN", "trainable": true, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_3_depthwise_relu", 0, 0, {}]]]}, {"name": "block_4_expand_BN", "class_name": "Conv2D", "config": {"name": "block_4_expand_BN", "trainable": true, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_3_project_BN", 0, 0, {}]]]}, {"name": "block_4_expand_relu", "class_name": "ReLU", "config": {"name": "block_4_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_4_expand_BN", 0, 0, {}]]]}, {"name": "block_4_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_4_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_4_expand_relu", 0, 0, {}]]]}, {"name": "block_4_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_4_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_4_depthwise_BN", 0, 0, {}]]]}, {"name": "block_4_project_BN", "class_name": "Conv2D", "config": {"name": "block_4_project_BN", "trainable": true, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_4_depthwise_relu", 0, 0, {}]]]}, {"name": "block_4_add", "class_name": "Add", "config": {"name": "block_4_add", "trainable": true}, "inbound_nodes": [[["block_3_project_BN", 0, 0, {}], ["block_4_project_BN", 0, 0, {}]]]}, {"name": "block_5_expand_BN", "class_name": "Conv2D", "config": {"name": "block_5_expand_BN", "trainable": true, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_4_add", 0, 0, {}]]]}, {"name": "block_5_expand_relu", "class_name": "ReLU", "config": {"name": "block_5_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_5_expand_BN", 0, 0, {}]]]}, {"name": "block_5_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_5_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_5_expand_relu", 0, 0, {}]]]}, {"name": "block_5_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_5_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_5_depthwise_BN", 0, 0, {}]]]}, {"name": "block_5_project_BN", "class_name": "Conv2D", "config": {"name": "block_5_project_BN", "trainable": true, "filters": 16, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_5_depthwise_relu", 0, 0, {}]]]}, {"name": "block_5_add", "class_name": "Add", "config": {"name": "block_5_add", "trainable": true}, "inbound_nodes": [[["block_4_add", 0, 0, {}], ["block_5_project_BN", 0, 0, {}]]]}, {"name": "block_6_expand_BN", "class_name": "Conv2D", "config": {"name": "block_6_expand_BN", "trainable": true, "filters": 96, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_5_add", 0, 0, {}]]]}, {"name": "block_6_expand_relu", "class_name": "ReLU", "config": {"name": "block_6_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_6_expand_BN", 0, 0, {}]]]}, {"name": "block_6_pad", "class_name": "ZeroPadding2D", "config": {"name": "block_6_pad", "trainable": true, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "inbound_nodes": [[["block_6_expand_relu", 0, 0, {}]]]}, {"name": "block_6_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_6_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_6_pad", 0, 0, {}]]]}, {"name": "block_6_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_6_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_6_depthwise_BN", 0, 0, {}]]]}, {"name": "block_6_project_BN", "class_name": "Conv2D", "config": {"name": "block_6_project_BN", "trainable": true, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_6_depthwise_relu", 0, 0, {}]]]}, {"name": "block_7_expand_BN", "class_name": "Conv2D", "config": {"name": "block_7_expand_BN", "trainable": true, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_6_project_BN", 0, 0, {}]]]}, {"name": "block_7_expand_relu", "class_name": "ReLU", "config": {"name": "block_7_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_7_expand_BN", 0, 0, {}]]]}, {"name": "block_7_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_7_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_7_expand_relu", 0, 0, {}]]]}, {"name": "block_7_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_7_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_7_depthwise_BN", 0, 0, {}]]]}, {"name": "block_7_project_BN", "class_name": "Conv2D", "config": {"name": "block_7_project_BN", "trainable": true, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_7_depthwise_relu", 0, 0, {}]]]}, {"name": "block_7_add", "class_name": "Add", "config": {"name": "block_7_add", "trainable": true}, "inbound_nodes": [[["block_6_project_BN", 0, 0, {}], ["block_7_project_BN", 0, 0, {}]]]}, {"name": "block_8_expand_BN", "class_name": "Conv2D", "config": {"name": "block_8_expand_BN", "trainable": true, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_7_add", 0, 0, {}]]]}, {"name": "block_8_expand_relu", "class_name": "ReLU", "config": {"name": "block_8_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_8_expand_BN", 0, 0, {}]]]}, {"name": "block_8_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_8_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_8_expand_relu", 0, 0, {}]]]}, {"name": "block_8_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_8_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_8_depthwise_BN", 0, 0, {}]]]}, {"name": "block_8_project_BN", "class_name": "Conv2D", "config": {"name": "block_8_project_BN", "trainable": true, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_8_depthwise_relu", 0, 0, {}]]]}, {"name": "block_8_add", "class_name": "Add", "config": {"name": "block_8_add", "trainable": true}, "inbound_nodes": [[["block_7_add", 0, 0, {}], ["block_8_project_BN", 0, 0, {}]]]}, {"name": "block_9_expand_BN", "class_name": "Conv2D", "config": {"name": "block_9_expand_BN", "trainable": true, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_8_add", 0, 0, {}]]]}, {"name": "block_9_expand_relu", "class_name": "ReLU", "config": {"name": "block_9_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_9_expand_BN", 0, 0, {}]]]}, {"name": "block_9_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_9_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_9_expand_relu", 0, 0, {}]]]}, {"name": "block_9_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_9_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_9_depthwise_BN", 0, 0, {}]]]}, {"name": "block_9_project_BN", "class_name": "Conv2D", "config": {"name": "block_9_project_BN", "trainable": true, "filters": 32, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_9_depthwise_relu", 0, 0, {}]]]}, {"name": "block_9_add", "class_name": "Add", "config": {"name": "block_9_add", "trainable": true}, "inbound_nodes": [[["block_8_add", 0, 0, {}], ["block_9_project_BN", 0, 0, {}]]]}, {"name": "block_10_expand_BN", "class_name": "Conv2D", "config": {"name": "block_10_expand_BN", "trainable": true, "filters": 192, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_9_add", 0, 0, {}]]]}, {"name": "block_10_expand_relu", "class_name": "ReLU", "config": {"name": "block_10_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_10_expand_BN", 0, 0, {}]]]}, {"name": "block_10_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_10_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_10_expand_relu", 0, 0, {}]]]}, {"name": "block_10_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_10_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_10_depthwise_BN", 0, 0, {}]]]}, {"name": "block_10_project_BN", "class_name": "Conv2D", "config": {"name": "block_10_project_BN", "trainable": true, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_10_depthwise_relu", 0, 0, {}]]]}, {"name": "block_11_expand_BN", "class_name": "Conv2D", "config": {"name": "block_11_expand_BN", "trainable": true, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_10_project_BN", 0, 0, {}]]]}, {"name": "block_11_expand_relu", "class_name": "ReLU", "config": {"name": "block_11_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_11_expand_BN", 0, 0, {}]]]}, {"name": "block_11_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_11_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_11_expand_relu", 0, 0, {}]]]}, {"name": "block_11_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_11_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_11_depthwise_BN", 0, 0, {}]]]}, {"name": "block_11_project_BN", "class_name": "Conv2D", "config": {"name": "block_11_project_BN", "trainable": true, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_11_depthwise_relu", 0, 0, {}]]]}, {"name": "block_11_add", "class_name": "Add", "config": {"name": "block_11_add", "trainable": true}, "inbound_nodes": [[["block_10_project_BN", 0, 0, {}], ["block_11_project_BN", 0, 0, {}]]]}, {"name": "block_12_expand_BN", "class_name": "Conv2D", "config": {"name": "block_12_expand_BN", "trainable": true, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_11_add", 0, 0, {}]]]}, {"name": "block_12_expand_relu", "class_name": "ReLU", "config": {"name": "block_12_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_12_expand_BN", 0, 0, {}]]]}, {"name": "block_12_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_12_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_12_expand_relu", 0, 0, {}]]]}, {"name": "block_12_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_12_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_12_depthwise_BN", 0, 0, {}]]]}, {"name": "block_12_project_BN", "class_name": "Conv2D", "config": {"name": "block_12_project_BN", "trainable": true, "filters": 48, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_12_depthwise_relu", 0, 0, {}]]]}, {"name": "block_12_add", "class_name": "Add", "config": {"name": "block_12_add", "trainable": true}, "inbound_nodes": [[["block_11_add", 0, 0, {}], ["block_12_project_BN", 0, 0, {}]]]}, {"name": "block_13_expand_BN", "class_name": "Conv2D", "config": {"name": "block_13_expand_BN", "trainable": true, "filters": 288, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_12_add", 0, 0, {}]]]}, {"name": "block_13_expand_relu", "class_name": "ReLU", "config": {"name": "block_13_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_13_expand_BN", 0, 0, {}]]]}, {"name": "block_13_pad", "class_name": "ZeroPadding2D", "config": {"name": "block_13_pad", "trainable": true, "padding": [[0, 1], [0, 1]], "data_format": "channels_last"}, "inbound_nodes": [[["block_13_expand_relu", 0, 0, {}]]]}, {"name": "block_13_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_13_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [2, 2], "padding": "valid", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_13_pad", 0, 0, {}]]]}, {"name": "block_13_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_13_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_13_depthwise_BN", 0, 0, {}]]]}, {"name": "block_13_project_BN", "class_name": "Conv2D", "config": {"name": "block_13_project_BN", "trainable": true, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_13_depthwise_relu", 0, 0, {}]]]}, {"name": "block_14_expand_BN", "class_name": "Conv2D", "config": {"name": "block_14_expand_BN", "trainable": true, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_13_project_BN", 0, 0, {}]]]}, {"name": "block_14_expand_relu", "class_name": "ReLU", "config": {"name": "block_14_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_14_expand_BN", 0, 0, {}]]]}, {"name": "block_14_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_14_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_14_expand_relu", 0, 0, {}]]]}, {"name": "block_14_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_14_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_14_depthwise_BN", 0, 0, {}]]]}, {"name": "block_14_project_BN", "class_name": "Conv2D", "config": {"name": "block_14_project_BN", "trainable": true, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_14_depthwise_relu", 0, 0, {}]]]}, {"name": "block_14_add", "class_name": "Add", "config": {"name": "block_14_add", "trainable": true}, "inbound_nodes": [[["block_13_project_BN", 0, 0, {}], ["block_14_project_BN", 0, 0, {}]]]}, {"name": "block_15_expand_BN", "class_name": "Conv2D", "config": {"name": "block_15_expand_BN", "trainable": true, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_14_add", 0, 0, {}]]]}, {"name": "block_15_expand_relu", "class_name": "ReLU", "config": {"name": "block_15_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_15_expand_BN", 0, 0, {}]]]}, {"name": "block_15_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_15_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_15_expand_relu", 0, 0, {}]]]}, {"name": "block_15_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_15_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_15_depthwise_BN", 0, 0, {}]]]}, {"name": "block_15_project_BN", "class_name": "Conv2D", "config": {"name": "block_15_project_BN", "trainable": true, "filters": 80, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_15_depthwise_relu", 0, 0, {}]]]}, {"name": "block_15_add", "class_name": "Add", "config": {"name": "block_15_add", "trainable": true}, "inbound_nodes": [[["block_14_add", 0, 0, {}], ["block_15_project_BN", 0, 0, {}]]]}, {"name": "block_16_expand_BN", "class_name": "Conv2D", "config": {"name": "block_16_expand_BN", "trainable": true, "filters": 480, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_15_add", 0, 0, {}]]]}, {"name": "block_16_expand_relu", "class_name": "ReLU", "config": {"name": "block_16_expand_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_16_expand_BN", 0, 0, {}]]]}, {"name": "block_16_depthwise_BN", "class_name": "DepthwiseConv2D", "config": {"name": "block_16_depthwise_BN", "trainable": true, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "bias_initializer": {"class_name": "Zeros", "config": {}}, "bias_regularizer": null, "activity_regularizer": null, "bias_constraint": null, "depth_multiplier": 1, "depthwise_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "depthwise_regularizer": null, "depthwise_constraint": null}, "inbound_nodes": [[["block_16_expand_relu", 0, 0, {}]]]}, {"name": "block_16_depthwise_relu", "class_name": "ReLU", "config": {"name": "block_16_depthwise_relu", "trainable": true, "max_value": 6.0, "negative_slope": 0.0, "threshold": 0.0}, "inbound_nodes": [[["block_16_depthwise_BN", 0, 0, {}]]]}, {"name": "block_16_project_BN", "class_name": "Conv2D", "config": {"name": "block_16_project_BN", "trainable": true, "filters": 160, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null}, "inbound_nodes": [[["block_16_depthwise_relu", 0, 0, {}]]]}, {"name": "conv2d_transpose_1", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_1", "trainable": true, "filters": 512, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 2.0, "mode": "fan_in", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["block_16_project_BN", 0, 0, {}]]]}, {"name": "batch_normalization_1", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_1", "trainable": true, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "inbound_nodes": [[["conv2d_transpose_1", 0, 0, {}]]]}, {"name": "dropout_1", "class_name": "Dropout", "config": {"name": "dropout_1", "trainable": true, "rate": 0.5, "noise_shape": null, "seed": null}, "inbound_nodes": [[["batch_normalization_1", 0, 0, {}]]]}, {"name": "activation_1", "class_name": "Activation", "config": {"name": "activation_1", "trainable": true, "activation": "relu"}, "inbound_nodes": [[["dropout_1", 0, 0, {}]]]}, {"name": "concatenate_1", "class_name": "Concatenate", "config": {"name": "concatenate_1", "trainable": true, "axis": 3}, "inbound_nodes": [[["activation_1", 0, 0, {}], ["block_13_expand_relu", 0, 0, {}]]]}, {"name": "conv2d_transpose_2", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_2", "trainable": true, "filters": 256, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 2.0, "mode": "fan_in", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["concatenate_1", 0, 0, {}]]]}, {"name": "batch_normalization_2", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_2", "trainable": true, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "inbound_nodes": [[["conv2d_transpose_2", 0, 0, {}]]]}, {"name": "dropout_2", "class_name": "Dropout", "config": {"name": "dropout_2", "trainable": true, "rate": 0.5, "noise_shape": null, "seed": null}, "inbound_nodes": [[["batch_normalization_2", 0, 0, {}]]]}, {"name": "activation_2", "class_name": "Activation", "config": {"name": "activation_2", "trainable": true, "activation": "relu"}, "inbound_nodes": [[["dropout_2", 0, 0, {}]]]}, {"name": "concatenate_2", "class_name": "Concatenate", "config": {"name": "concatenate_2", "trainable": true, "axis": 3}, "inbound_nodes": [[["activation_2", 0, 0, {}], ["block_6_expand_relu", 0, 0, {}]]]}, {"name": "conv2d_transpose_3", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_3", "trainable": true, "filters": 128, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 2.0, "mode": "fan_in", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["concatenate_2", 0, 0, {}]]]}, {"name": "batch_normalization_3", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_3", "trainable": true, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "inbound_nodes": [[["conv2d_transpose_3", 0, 0, {}]]]}, {"name": "dropout_3", "class_name": "Dropout", "config": {"name": "dropout_3", "trainable": true, "rate": 0.5, "noise_shape": null, "seed": null}, "inbound_nodes": [[["batch_normalization_3", 0, 0, {}]]]}, {"name": "activation_3", "class_name": "Activation", "config": {"name": "activation_3", "trainable": true, "activation": "relu"}, "inbound_nodes": [[["dropout_3", 0, 0, {}]]]}, {"name": "concatenate_3", "class_name": "Concatenate", "config": {"name": "concatenate_3", "trainable": true, "axis": 3}, "inbound_nodes": [[["activation_3", 0, 0, {}], ["block_3_expand_relu", 0, 0, {}]]]}, {"name": "conv2d_transpose_4", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_4", "trainable": true, "filters": 64, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 2.0, "mode": "fan_in", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["concatenate_3", 0, 0, {}]]]}, {"name": "batch_normalization_4", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_4", "trainable": true, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "inbound_nodes": [[["conv2d_transpose_4", 0, 0, {}]]]}, {"name": "dropout_4", "class_name": "Dropout", "config": {"name": "dropout_4", "trainable": true, "rate": 0.5, "noise_shape": null, "seed": null}, "inbound_nodes": [[["batch_normalization_4", 0, 0, {}]]]}, {"name": "activation_4", "class_name": "Activation", "config": {"name": "activation_4", "trainable": true, "activation": "relu"}, "inbound_nodes": [[["dropout_4", 0, 0, {}]]]}, {"name": "concatenate_4", "class_name": "Concatenate", "config": {"name": "concatenate_4", "trainable": true, "axis": 3}, "inbound_nodes": [[["activation_4", 0, 0, {}], ["block_1_expand_relu", 0, 0, {}]]]}, {"name": "conv2d_transpose_5", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_5", "trainable": true, "filters": 32, "kernel_size": [3, 3], "strides": [2, 2], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 2.0, "mode": "fan_in", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["concatenate_4", 0, 0, {}]]]}, {"name": "batch_normalization_5", "class_name": "BatchNormalization", "config": {"name": "batch_normalization_5", "trainable": true, "axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null}, "inbound_nodes": [[["conv2d_transpose_5", 0, 0, {}]]]}, {"name": "activation_5", "class_name": "Activation", "config": {"name": "activation_5", "trainable": true, "activation": "relu"}, "inbound_nodes": [[["batch_normalization_5", 0, 0, {}]]]}, {"name": "conv2d_transpose_6", "class_name": "Conv2DTranspose", "config": {"name": "conv2d_transpose_6", "trainable": true, "filters": 1, "kernel_size": [1, 1], "strides": [1, 1], "padding": "same", "data_format": "channels_last", "dilation_rate": [1, 1], "activation": "linear", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1.0, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "output_padding": null}, "inbound_nodes": [[["activation_5", 0, 0, {}]]]}, {"name": "op", "class_name": "Activation", "config": {"name": "op", "trainable": true, "activation": "sigmoid"}, "inbound_nodes": [[["conv2d_transpose_6", 0, 0, {}]]]}], "input_layers": [["input_1", 0, 0]], "output_layers": [["op", 0, 0]]}}}, "weightsManifest": [{"paths": ["group1-shard1of4.bin", "group1-shard2of4.bin", "group1-shard3of4.bin", "group1-shard4of4.bin"], "weights": [{"name": "batch_normalization_1/gamma", "shape": [512], "dtype": "float32"}, {"name": "batch_normalization_1/beta", "shape": [512], "dtype": "float32"}, {"name": "batch_normalization_1/moving_mean", "shape": [512], "dtype": "float32"}, {"name": "batch_normalization_1/moving_variance", "shape": [512], "dtype": "float32"}, {"name": "batch_normalization_2/gamma", "shape": [256], "dtype": "float32"}, {"name": "batch_normalization_2/beta", "shape": [256], "dtype": "float32"}, {"name": "batch_normalization_2/moving_mean", "shape": [256], "dtype": "float32"}, {"name": "batch_normalization_2/moving_variance", "shape": [256], "dtype": "float32"}, {"name": "batch_normalization_3/gamma", "shape": [128], "dtype": "float32"}, {"name": "batch_normalization_3/beta", "shape": [128], "dtype": "float32"}, {"name": "batch_normalization_3/moving_mean", "shape": [128], "dtype": "float32"}, {"name": "batch_normalization_3/moving_variance", "shape": [128], "dtype": "float32"}, {"name": "batch_normalization_4/gamma", "shape": [64], "dtype": "float32"}, {"name": "batch_normalization_4/beta", "shape": [64], "dtype": "float32"}, {"name": "batch_normalization_4/moving_mean", "shape": [64], "dtype": "float32"}, {"name": "batch_normalization_4/moving_variance", "shape": [64], "dtype": "float32"}, {"name": "batch_normalization_5/gamma", "shape": [32], "dtype": "float32"}, {"name": "batch_normalization_5/beta", "shape": [32], "dtype": "float32"}, {"name": "batch_normalization_5/moving_mean", "shape": [32], "dtype": "float32"}, {"name": "batch_normalization_5/moving_variance", "shape": [32], "dtype": "float32"}, {"name": "block_10_depthwise_BN/depthwise_kernel", "shape": [3, 3, 192, 1], "dtype": "float32"}, {"name": "block_10_depthwise_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_10_expand_BN/kernel", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "block_10_expand_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_10_project_BN/kernel", "shape": [1, 1, 192, 48], "dtype": "float32"}, {"name": "block_10_project_BN/bias", "shape": [48], "dtype": "float32"}, {"name": "block_11_depthwise_BN/depthwise_kernel", "shape": [3, 3, 288, 1], "dtype": "float32"}, {"name": "block_11_depthwise_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_11_expand_BN/kernel", "shape": [1, 1, 48, 288], "dtype": "float32"}, {"name": "block_11_expand_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_11_project_BN/kernel", "shape": [1, 1, 288, 48], "dtype": "float32"}, {"name": "block_11_project_BN/bias", "shape": [48], "dtype": "float32"}, {"name": "block_12_depthwise_BN/depthwise_kernel", "shape": [3, 3, 288, 1], "dtype": "float32"}, {"name": "block_12_depthwise_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_12_expand_BN/kernel", "shape": [1, 1, 48, 288], "dtype": "float32"}, {"name": "block_12_expand_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_12_project_BN/kernel", "shape": [1, 1, 288, 48], "dtype": "float32"}, {"name": "block_12_project_BN/bias", "shape": [48], "dtype": "float32"}, {"name": "block_13_depthwise_BN/depthwise_kernel", "shape": [3, 3, 288, 1], "dtype": "float32"}, {"name": "block_13_depthwise_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_13_expand_BN/kernel", "shape": [1, 1, 48, 288], "dtype": "float32"}, {"name": "block_13_expand_BN/bias", "shape": [288], "dtype": "float32"}, {"name": "block_13_project_BN/kernel", "shape": [1, 1, 288, 80], "dtype": "float32"}, {"name": "block_13_project_BN/bias", "shape": [80], "dtype": "float32"}, {"name": "block_14_depthwise_BN/depthwise_kernel", "shape": [3, 3, 480, 1], "dtype": "float32"}, {"name": "block_14_depthwise_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_14_expand_BN/kernel", "shape": [1, 1, 80, 480], "dtype": "float32"}, {"name": "block_14_expand_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_14_project_BN/kernel", "shape": [1, 1, 480, 80], "dtype": "float32"}, {"name": "block_14_project_BN/bias", "shape": [80], "dtype": "float32"}, {"name": "block_15_depthwise_BN/depthwise_kernel", "shape": [3, 3, 480, 1], "dtype": "float32"}, {"name": "block_15_depthwise_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_15_expand_BN/kernel", "shape": [1, 1, 80, 480], "dtype": "float32"}, {"name": "block_15_expand_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_15_project_BN/kernel", "shape": [1, 1, 480, 80], "dtype": "float32"}, {"name": "block_15_project_BN/bias", "shape": [80], "dtype": "float32"}, {"name": "block_16_depthwise_BN/depthwise_kernel", "shape": [3, 3, 480, 1], "dtype": "float32"}, {"name": "block_16_depthwise_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_16_expand_BN/kernel", "shape": [1, 1, 80, 480], "dtype": "float32"}, {"name": "block_16_expand_BN/bias", "shape": [480], "dtype": "float32"}, {"name": "block_16_project_BN/kernel", "shape": [1, 1, 480, 160], "dtype": "float32"}, {"name": "block_16_project_BN/bias", "shape": [160], "dtype": "float32"}, {"name": "block_1_depthwise_BN/depthwise_kernel", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "block_1_depthwise_BN/bias", "shape": [48], "dtype": "float32"}, {"name": "block_1_expand_BN/kernel", "shape": [1, 1, 8, 48], "dtype": "float32"}, {"name": "block_1_expand_BN/bias", "shape": [48], "dtype": "float32"}, {"name": "block_1_project_BN/kernel", "shape": [1, 1, 48, 16], "dtype": "float32"}, {"name": "block_1_project_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "block_2_depthwise_BN/depthwise_kernel", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "block_2_depthwise_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_2_expand_BN/kernel", "shape": [1, 1, 16, 96], "dtype": "float32"}, {"name": "block_2_expand_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_2_project_BN/kernel", "shape": [1, 1, 96, 16], "dtype": "float32"}, {"name": "block_2_project_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "block_3_depthwise_BN/depthwise_kernel", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "block_3_depthwise_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_3_expand_BN/kernel", "shape": [1, 1, 16, 96], "dtype": "float32"}, {"name": "block_3_expand_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_3_project_BN/kernel", "shape": [1, 1, 96, 16], "dtype": "float32"}, {"name": "block_3_project_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "block_4_depthwise_BN/depthwise_kernel", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "block_4_depthwise_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_4_expand_BN/kernel", "shape": [1, 1, 16, 96], "dtype": "float32"}, {"name": "block_4_expand_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_4_project_BN/kernel", "shape": [1, 1, 96, 16], "dtype": "float32"}, {"name": "block_4_project_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "block_5_depthwise_BN/depthwise_kernel", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "block_5_depthwise_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_5_expand_BN/kernel", "shape": [1, 1, 16, 96], "dtype": "float32"}, {"name": "block_5_expand_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_5_project_BN/kernel", "shape": [1, 1, 96, 16], "dtype": "float32"}, {"name": "block_5_project_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "block_6_depthwise_BN/depthwise_kernel", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "block_6_depthwise_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_6_expand_BN/kernel", "shape": [1, 1, 16, 96], "dtype": "float32"}, {"name": "block_6_expand_BN/bias", "shape": [96], "dtype": "float32"}, {"name": "block_6_project_BN/kernel", "shape": [1, 1, 96, 32], "dtype": "float32"}, {"name": "block_6_project_BN/bias", "shape": [32], "dtype": "float32"}, {"name": "block_7_depthwise_BN/depthwise_kernel", "shape": [3, 3, 192, 1], "dtype": "float32"}, {"name": "block_7_depthwise_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_7_expand_BN/kernel", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "block_7_expand_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_7_project_BN/kernel", "shape": [1, 1, 192, 32], "dtype": "float32"}, {"name": "block_7_project_BN/bias", "shape": [32], "dtype": "float32"}, {"name": "block_8_depthwise_BN/depthwise_kernel", "shape": [3, 3, 192, 1], "dtype": "float32"}, {"name": "block_8_depthwise_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_8_expand_BN/kernel", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "block_8_expand_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_8_project_BN/kernel", "shape": [1, 1, 192, 32], "dtype": "float32"}, {"name": "block_8_project_BN/bias", "shape": [32], "dtype": "float32"}, {"name": "block_9_depthwise_BN/depthwise_kernel", "shape": [3, 3, 192, 1], "dtype": "float32"}, {"name": "block_9_depthwise_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_9_expand_BN/kernel", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "block_9_expand_BN/bias", "shape": [192], "dtype": "float32"}, {"name": "block_9_project_BN/kernel", "shape": [1, 1, 192, 32], "dtype": "float32"}, {"name": "block_9_project_BN/bias", "shape": [32], "dtype": "float32"}, {"name": "bn_Conv1/kernel", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "bn_Conv1/bias", "shape": [16], "dtype": "float32"}, {"name": "conv2d_transpose_1/kernel", "shape": [3, 3, 512, 160], "dtype": "float32"}, {"name": "conv2d_transpose_1/bias", "shape": [512], "dtype": "float32"}, {"name": "conv2d_transpose_2/kernel", "shape": [3, 3, 256, 800], "dtype": "float32"}, {"name": "conv2d_transpose_2/bias", "shape": [256], "dtype": "float32"}, {"name": "conv2d_transpose_3/kernel", "shape": [3, 3, 128, 352], "dtype": "float32"}, {"name": "conv2d_transpose_3/bias", "shape": [128], "dtype": "float32"}, {"name": "conv2d_transpose_4/kernel", "shape": [3, 3, 64, 224], "dtype": "float32"}, {"name": "conv2d_transpose_4/bias", "shape": [64], "dtype": "float32"}, {"name": "conv2d_transpose_5/kernel", "shape": [3, 3, 32, 112], "dtype": "float32"}, {"name": "conv2d_transpose_5/bias", "shape": [32], "dtype": "float32"}, {"name": "conv2d_transpose_6/kernel", "shape": [1, 1, 1, 32], "dtype": "float32"}, {"name": "conv2d_transpose_6/bias", "shape": [1], "dtype": "float32"}, {"name": "expanded_conv_depthwise_BN/depthwise_kernel", "shape": [3, 3, 16, 1], "dtype": "float32"}, {"name": "expanded_conv_depthwise_BN/bias", "shape": [16], "dtype": "float32"}, {"name": "expanded_conv_project_BN/kernel", "shape": [1, 1, 16, 8], "dtype": "float32"}, {"name": "expanded_conv_project_BN/bias", "shape": [8], "dtype": "float32"}]}]}