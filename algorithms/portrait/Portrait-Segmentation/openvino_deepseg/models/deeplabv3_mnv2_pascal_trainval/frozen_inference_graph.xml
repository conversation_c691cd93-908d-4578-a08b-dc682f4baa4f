<?xml version="1.0" ?>
<net batch="1" name="frozen_inference_graph" version="6">
	<layers>
		<layer id="0" name="ImageTensor" precision="FP32" type="Input">
			<output>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="ImageTensor/Transpose" precision="FP32" type="Permute">
			<data order="0,2,3,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="Squeeze/Dims/Output_0/Data__const" precision="I32" type="Const">
			<output>
				<port id="1">
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<custom offset="0" size="4"/>
			</blobs>
		</layer>
		<layer id="3" name="Squeeze" precision="FP32" type="Squeeze">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="ExpandDims/dim/Output_0/Data__const" precision="I32" type="Const">
			<output>
				<port id="1">
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<custom offset="0" size="4"/>
			</blobs>
		</layer>
		<layer id="5" name="ExpandDims" precision="FP32" type="Unsqueeze">
			<input>
				<port id="0">
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="ExpandDims/Transpose" precision="FP32" type="Permute">
			<data order="0,3,1,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="ResizeBilinear" precision="FP32" type="Interp">
			<data align_corners="1" factor="1.0" height="0" pad_beg="0" pad_end="0" width="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="ResizeBilinear/Transpose" precision="FP32" type="Permute">
			<data order="0,2,3,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="Squeeze_1/Dims/Output_0/Data__const" precision="I32" type="Const">
			<output>
				<port id="1">
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<custom offset="0" size="4"/>
			</blobs>
		</layer>
		<layer id="10" name="Squeeze_1" precision="FP32" type="Squeeze">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="ExpandDims_1/dim/Output_0/Data__const" precision="I32" type="Const">
			<output>
				<port id="1">
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<custom offset="0" size="4"/>
			</blobs>
		</layer>
		<layer id="12" name="ExpandDims_1" precision="FP32" type="Unsqueeze">
			<input>
				<port id="0">
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="ExpandDims_1/Transpose" precision="FP32" type="Permute">
			<data order="0,3,1,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="mul_1/fused_power" precision="FP32" type="Power">
			<data power="1" scale="0.007843137718737125" shift="-1.0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="MobilenetV2/Conv/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="3,3" output="32" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>3</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
			<blobs>
				<weights offset="4" size="3456"/>
				<biases offset="3460" size="128"/>
			</blobs>
		</layer>
		<layer id="16" name="MobilenetV2/Conv/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="MobilenetV2/expanded_conv/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="32" kernel="3,3" output="32" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
			<blobs>
				<weights offset="3588" size="1152"/>
				<biases offset="4740" size="128"/>
			</blobs>
		</layer>
		<layer id="18" name="MobilenetV2/expanded_conv/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="MobilenetV2/expanded_conv/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="16" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>16</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
			<blobs>
				<weights offset="4868" size="2048"/>
				<biases offset="6916" size="64"/>
			</blobs>
		</layer>
		<layer id="20" name="MobilenetV2/expanded_conv_1/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="96" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>96</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
			<blobs>
				<weights offset="6980" size="6144"/>
				<biases offset="13124" size="384"/>
			</blobs>
		</layer>
		<layer id="21" name="MobilenetV2/expanded_conv_1/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>96</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="MobilenetV2/expanded_conv_1/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="96" kernel="3,3" output="96" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>257</dim>
					<dim>257</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>96</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="13508" size="3456"/>
				<biases offset="16964" size="384"/>
			</blobs>
		</layer>
		<layer id="23" name="MobilenetV2/expanded_conv_1/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>96</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="MobilenetV2/expanded_conv_1/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="24" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="17348" size="9216"/>
				<biases offset="26564" size="96"/>
			</blobs>
		</layer>
		<layer id="25" name="MobilenetV2/expanded_conv_2/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="144" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="26660" size="13824"/>
				<biases offset="40484" size="576"/>
			</blobs>
		</layer>
		<layer id="26" name="MobilenetV2/expanded_conv_2/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="MobilenetV2/expanded_conv_2/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="144" kernel="3,3" output="144" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="41060" size="5184"/>
				<biases offset="46244" size="576"/>
			</blobs>
		</layer>
		<layer id="28" name="MobilenetV2/expanded_conv_2/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="MobilenetV2/expanded_conv_2/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="24" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="46820" size="13824"/>
				<biases offset="60644" size="96"/>
			</blobs>
		</layer>
		<layer id="30" name="MobilenetV2/expanded_conv_2/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="MobilenetV2/expanded_conv_3/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="144" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>24</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
			<blobs>
				<weights offset="60740" size="13824"/>
				<biases offset="74564" size="576"/>
			</blobs>
		</layer>
		<layer id="32" name="MobilenetV2/expanded_conv_3/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="MobilenetV2/expanded_conv_3/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="144" kernel="3,3" output="144" pads_begin="1,1" pads_end="1,1" strides="2,2"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>129</dim>
					<dim>129</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>144</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="75140" size="5184"/>
				<biases offset="80324" size="576"/>
			</blobs>
		</layer>
		<layer id="34" name="MobilenetV2/expanded_conv_3/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>144</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="MobilenetV2/expanded_conv_3/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="32" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>144</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="80900" size="18432"/>
				<biases offset="99332" size="128"/>
			</blobs>
		</layer>
		<layer id="36" name="MobilenetV2/expanded_conv_4/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="192" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="99460" size="24576"/>
				<biases offset="124036" size="768"/>
			</blobs>
		</layer>
		<layer id="37" name="MobilenetV2/expanded_conv_4/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="MobilenetV2/expanded_conv_4/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="192" kernel="3,3" output="192" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="124804" size="6912"/>
				<biases offset="131716" size="768"/>
			</blobs>
		</layer>
		<layer id="39" name="MobilenetV2/expanded_conv_4/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="MobilenetV2/expanded_conv_4/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="32" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="132484" size="24576"/>
				<biases offset="157060" size="128"/>
			</blobs>
		</layer>
		<layer id="41" name="MobilenetV2/expanded_conv_4/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="MobilenetV2/expanded_conv_5/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="192" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="157188" size="24576"/>
				<biases offset="181764" size="768"/>
			</blobs>
		</layer>
		<layer id="43" name="MobilenetV2/expanded_conv_5/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="MobilenetV2/expanded_conv_5/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="192" kernel="3,3" output="192" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="182532" size="6912"/>
				<biases offset="189444" size="768"/>
			</blobs>
		</layer>
		<layer id="45" name="MobilenetV2/expanded_conv_5/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="MobilenetV2/expanded_conv_5/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="32" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="190212" size="24576"/>
				<biases offset="214788" size="128"/>
			</blobs>
		</layer>
		<layer id="47" name="MobilenetV2/expanded_conv_5/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="MobilenetV2/expanded_conv_6/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="192" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="214916" size="24576"/>
				<biases offset="239492" size="768"/>
			</blobs>
		</layer>
		<layer id="49" name="MobilenetV2/expanded_conv_6/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="MobilenetV2/expanded_conv_6/depthwise/depthwise" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="192" kernel="3,3" output="192" pads_begin="1,1" pads_end="1,1" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="240260" size="6912"/>
				<biases offset="247172" size="768"/>
			</blobs>
		</layer>
		<layer id="51" name="MobilenetV2/expanded_conv_6/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="MobilenetV2/expanded_conv_6/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="64" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="247940" size="49152"/>
				<biases offset="297092" size="256"/>
			</blobs>
		</layer>
		<layer id="53" name="MobilenetV2/expanded_conv_7/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="384" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="297348" size="98304"/>
				<biases offset="395652" size="1536"/>
			</blobs>
		</layer>
		<layer id="54" name="MobilenetV2/expanded_conv_7/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="MobilenetV2/expanded_conv_7/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="384" kernel="3,3" output="384" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="397188" size="13824"/>
				<biases offset="411012" size="1536"/>
			</blobs>
		</layer>
		<layer id="56" name="MobilenetV2/expanded_conv_7/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="MobilenetV2/expanded_conv_7/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="64" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="412548" size="98304"/>
				<biases offset="510852" size="256"/>
			</blobs>
		</layer>
		<layer id="58" name="MobilenetV2/expanded_conv_7/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="MobilenetV2/expanded_conv_8/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="384" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="511108" size="98304"/>
				<biases offset="609412" size="1536"/>
			</blobs>
		</layer>
		<layer id="60" name="MobilenetV2/expanded_conv_8/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="MobilenetV2/expanded_conv_8/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="384" kernel="3,3" output="384" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="610948" size="13824"/>
				<biases offset="624772" size="1536"/>
			</blobs>
		</layer>
		<layer id="62" name="MobilenetV2/expanded_conv_8/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="MobilenetV2/expanded_conv_8/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="64" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="626308" size="98304"/>
				<biases offset="724612" size="256"/>
			</blobs>
		</layer>
		<layer id="64" name="MobilenetV2/expanded_conv_8/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="MobilenetV2/expanded_conv_9/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="384" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="724868" size="98304"/>
				<biases offset="823172" size="1536"/>
			</blobs>
		</layer>
		<layer id="66" name="MobilenetV2/expanded_conv_9/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="MobilenetV2/expanded_conv_9/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="384" kernel="3,3" output="384" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="824708" size="13824"/>
				<biases offset="838532" size="1536"/>
			</blobs>
		</layer>
		<layer id="68" name="MobilenetV2/expanded_conv_9/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="MobilenetV2/expanded_conv_9/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="64" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="840068" size="98304"/>
				<biases offset="938372" size="256"/>
			</blobs>
		</layer>
		<layer id="70" name="MobilenetV2/expanded_conv_9/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="MobilenetV2/expanded_conv_10/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="384" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="938628" size="98304"/>
				<biases offset="1036932" size="1536"/>
			</blobs>
		</layer>
		<layer id="72" name="MobilenetV2/expanded_conv_10/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="MobilenetV2/expanded_conv_10/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="384" kernel="3,3" output="384" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1038468" size="13824"/>
				<biases offset="1052292" size="1536"/>
			</blobs>
		</layer>
		<layer id="74" name="MobilenetV2/expanded_conv_10/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="MobilenetV2/expanded_conv_10/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="96" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1053828" size="147456"/>
				<biases offset="1201284" size="384"/>
			</blobs>
		</layer>
		<layer id="76" name="MobilenetV2/expanded_conv_11/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="576" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1201668" size="221184"/>
				<biases offset="1422852" size="2304"/>
			</blobs>
		</layer>
		<layer id="77" name="MobilenetV2/expanded_conv_11/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="MobilenetV2/expanded_conv_11/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="576" kernel="3,3" output="576" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1425156" size="20736"/>
				<biases offset="1445892" size="2304"/>
			</blobs>
		</layer>
		<layer id="79" name="MobilenetV2/expanded_conv_11/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="MobilenetV2/expanded_conv_11/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="96" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1448196" size="221184"/>
				<biases offset="1669380" size="384"/>
			</blobs>
		</layer>
		<layer id="81" name="MobilenetV2/expanded_conv_11/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="MobilenetV2/expanded_conv_12/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="576" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1669764" size="221184"/>
				<biases offset="1890948" size="2304"/>
			</blobs>
		</layer>
		<layer id="83" name="MobilenetV2/expanded_conv_12/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="MobilenetV2/expanded_conv_12/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="576" kernel="3,3" output="576" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1893252" size="20736"/>
				<biases offset="1913988" size="2304"/>
			</blobs>
		</layer>
		<layer id="85" name="MobilenetV2/expanded_conv_12/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="MobilenetV2/expanded_conv_12/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="96" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="1916292" size="221184"/>
				<biases offset="2137476" size="384"/>
			</blobs>
		</layer>
		<layer id="87" name="MobilenetV2/expanded_conv_12/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="MobilenetV2/expanded_conv_13/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="576" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="2137860" size="221184"/>
				<biases offset="2359044" size="2304"/>
			</blobs>
		</layer>
		<layer id="89" name="MobilenetV2/expanded_conv_13/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="MobilenetV2/expanded_conv_13/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="2,2" group="576" kernel="3,3" output="576" pads_begin="2,2" pads_end="2,2" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="2361348" size="20736"/>
				<biases offset="2382084" size="2304"/>
			</blobs>
		</layer>
		<layer id="91" name="MobilenetV2/expanded_conv_13/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="MobilenetV2/expanded_conv_13/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="160" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>576</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="2384388" size="368640"/>
				<biases offset="2753028" size="640"/>
			</blobs>
		</layer>
		<layer id="93" name="MobilenetV2/expanded_conv_14/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="960" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="2753668" size="614400"/>
				<biases offset="3368068" size="3840"/>
			</blobs>
		</layer>
		<layer id="94" name="MobilenetV2/expanded_conv_14/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="MobilenetV2/expanded_conv_14/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="4,4" group="960" kernel="3,3" output="960" pads_begin="4,4" pads_end="4,4" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="3371908" size="34560"/>
				<biases offset="3406468" size="3840"/>
			</blobs>
		</layer>
		<layer id="96" name="MobilenetV2/expanded_conv_14/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="MobilenetV2/expanded_conv_14/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="160" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="3410308" size="614400"/>
				<biases offset="4024708" size="640"/>
			</blobs>
		</layer>
		<layer id="98" name="MobilenetV2/expanded_conv_14/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="MobilenetV2/expanded_conv_15/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="960" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="4025348" size="614400"/>
				<biases offset="4639748" size="3840"/>
			</blobs>
		</layer>
		<layer id="100" name="MobilenetV2/expanded_conv_15/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="MobilenetV2/expanded_conv_15/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="4,4" group="960" kernel="3,3" output="960" pads_begin="4,4" pads_end="4,4" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="4643588" size="34560"/>
				<biases offset="4678148" size="3840"/>
			</blobs>
		</layer>
		<layer id="102" name="MobilenetV2/expanded_conv_15/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="MobilenetV2/expanded_conv_15/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="160" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="4681988" size="614400"/>
				<biases offset="5296388" size="640"/>
			</blobs>
		</layer>
		<layer id="104" name="MobilenetV2/expanded_conv_15/add" precision="FP32" type="Eltwise">
			<data operation="sum"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="MobilenetV2/expanded_conv_16/expand/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="960" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>160</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="5297028" size="614400"/>
				<biases offset="5911428" size="3840"/>
			</blobs>
		</layer>
		<layer id="106" name="MobilenetV2/expanded_conv_16/expand/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="MobilenetV2/expanded_conv_16/depthwise/depthwise" precision="FP32" type="Convolution">
			<data dilations="4,4" group="960" kernel="3,3" output="960" pads_begin="4,4" pads_end="4,4" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="5915268" size="34560"/>
				<biases offset="5949828" size="3840"/>
			</blobs>
		</layer>
		<layer id="108" name="MobilenetV2/expanded_conv_16/depthwise/Relu6" precision="FP32" type="Clamp">
			<data max="6" min="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="MobilenetV2/expanded_conv_16/project/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="320" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>960</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>320</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="5953668" size="1228800"/>
				<biases offset="7182468" size="1280"/>
			</blobs>
		</layer>
		<layer id="110" name="AvgPool2D/AvgPool" precision="FP32" type="Pooling">
			<data auto_pad="valid" exclude-pad="true" kernel="65,65" pads_begin="0,0" pads_end="0,0" pool-method="avg" strides="65,65"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="image_pooling/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="256" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<weights offset="7183748" size="327680"/>
				<biases offset="7511428" size="1024"/>
			</blobs>
		</layer>
		<layer id="112" name="image_pooling/Relu" precision="FP32" type="ReLU">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="ResizeBilinear_1" precision="FP32" type="Interp">
			<data align_corners="1" factor="65.0" height="0" pad_beg="0" pad_end="0" width="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="aspp0/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="256" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>320</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="7512452" size="327680"/>
				<biases offset="7840132" size="1024"/>
			</blobs>
		</layer>
		<layer id="115" name="aspp0/Relu" precision="FP32" type="ReLU">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="concat" precision="FP32" type="Concat">
			<data axis="1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
				<port id="1">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>512</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="concat_projection/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="256" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>512</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="7841156" size="524288"/>
				<biases offset="8365444" size="1024"/>
			</blobs>
		</layer>
		<layer id="118" name="concat_projection/Relu" precision="FP32" type="ReLU">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="logits/semantic/Conv2D" precision="FP32" type="Convolution">
			<data auto_pad="same_upper" dilations="1,1" group="1" kernel="1,1" output="21" pads_begin="0,0" pads_end="0,0" strides="1,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="3">
					<dim>1</dim>
					<dim>21</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
			<blobs>
				<weights offset="8366468" size="21504"/>
				<biases offset="8387972" size="84"/>
			</blobs>
		</layer>
		<layer id="120" name="ResizeBilinear_2" precision="FP32" type="Interp">
			<data align_corners="1" factor="1.0" height="0" pad_beg="0" pad_end="0" width="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>21</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>21</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="ResizeBilinear_3" precision="FP32" type="Interp">
			<data align_corners="1" factor="7.892307692307693" height="0" pad_beg="0" pad_end="0" width="0"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>21</dim>
					<dim>65</dim>
					<dim>65</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>21</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="ArgMax" precision="FP32" type="ArgMax">
			<data axis="1" out_max_val="0" top_k="1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>21</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="ArgMax/Transpose" precision="FP32" type="Permute">
			<data order="0,2,3,1"/>
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</input>
			<output>
				<port id="1">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="ArgMax/dimension/Output_0/Data__const" precision="I32" type="Const">
			<output>
				<port id="1">
					<dim>1</dim>
				</port>
			</output>
			<blobs>
				<custom offset="8388056" size="4"/>
			</blobs>
		</layer>
		<layer id="125" name="ArgMax/Squeeze" precision="FP32" type="Squeeze">
			<input>
				<port id="0">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
					<dim>1</dim>
				</port>
				<port id="1">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2">
					<dim>1</dim>
					<dim>513</dim>
					<dim>513</dim>
				</port>
			</output>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="1" to-port="0"/>
		<edge from-layer="1" from-port="1" to-layer="3" to-port="0"/>
		<edge from-layer="2" from-port="1" to-layer="3" to-port="1"/>
		<edge from-layer="3" from-port="2" to-layer="5" to-port="0"/>
		<edge from-layer="4" from-port="1" to-layer="5" to-port="1"/>
		<edge from-layer="5" from-port="2" to-layer="6" to-port="0"/>
		<edge from-layer="6" from-port="1" to-layer="7" to-port="0"/>
		<edge from-layer="7" from-port="1" to-layer="8" to-port="0"/>
		<edge from-layer="8" from-port="1" to-layer="10" to-port="0"/>
		<edge from-layer="9" from-port="1" to-layer="10" to-port="1"/>
		<edge from-layer="10" from-port="2" to-layer="12" to-port="0"/>
		<edge from-layer="11" from-port="1" to-layer="12" to-port="1"/>
		<edge from-layer="12" from-port="2" to-layer="13" to-port="0"/>
		<edge from-layer="13" from-port="1" to-layer="14" to-port="0"/>
		<edge from-layer="14" from-port="1" to-layer="15" to-port="0"/>
		<edge from-layer="15" from-port="3" to-layer="16" to-port="0"/>
		<edge from-layer="16" from-port="1" to-layer="17" to-port="0"/>
		<edge from-layer="17" from-port="3" to-layer="18" to-port="0"/>
		<edge from-layer="18" from-port="1" to-layer="19" to-port="0"/>
		<edge from-layer="19" from-port="3" to-layer="20" to-port="0"/>
		<edge from-layer="20" from-port="3" to-layer="21" to-port="0"/>
		<edge from-layer="21" from-port="1" to-layer="22" to-port="0"/>
		<edge from-layer="22" from-port="3" to-layer="23" to-port="0"/>
		<edge from-layer="23" from-port="1" to-layer="24" to-port="0"/>
		<edge from-layer="24" from-port="3" to-layer="25" to-port="0"/>
		<edge from-layer="25" from-port="3" to-layer="26" to-port="0"/>
		<edge from-layer="26" from-port="1" to-layer="27" to-port="0"/>
		<edge from-layer="27" from-port="3" to-layer="28" to-port="0"/>
		<edge from-layer="28" from-port="1" to-layer="29" to-port="0"/>
		<edge from-layer="24" from-port="3" to-layer="30" to-port="0"/>
		<edge from-layer="29" from-port="3" to-layer="30" to-port="1"/>
		<edge from-layer="30" from-port="2" to-layer="31" to-port="0"/>
		<edge from-layer="31" from-port="3" to-layer="32" to-port="0"/>
		<edge from-layer="32" from-port="1" to-layer="33" to-port="0"/>
		<edge from-layer="33" from-port="3" to-layer="34" to-port="0"/>
		<edge from-layer="34" from-port="1" to-layer="35" to-port="0"/>
		<edge from-layer="35" from-port="3" to-layer="36" to-port="0"/>
		<edge from-layer="36" from-port="3" to-layer="37" to-port="0"/>
		<edge from-layer="37" from-port="1" to-layer="38" to-port="0"/>
		<edge from-layer="38" from-port="3" to-layer="39" to-port="0"/>
		<edge from-layer="39" from-port="1" to-layer="40" to-port="0"/>
		<edge from-layer="35" from-port="3" to-layer="41" to-port="0"/>
		<edge from-layer="40" from-port="3" to-layer="41" to-port="1"/>
		<edge from-layer="41" from-port="2" to-layer="42" to-port="0"/>
		<edge from-layer="42" from-port="3" to-layer="43" to-port="0"/>
		<edge from-layer="43" from-port="1" to-layer="44" to-port="0"/>
		<edge from-layer="44" from-port="3" to-layer="45" to-port="0"/>
		<edge from-layer="45" from-port="1" to-layer="46" to-port="0"/>
		<edge from-layer="41" from-port="2" to-layer="47" to-port="0"/>
		<edge from-layer="46" from-port="3" to-layer="47" to-port="1"/>
		<edge from-layer="47" from-port="2" to-layer="48" to-port="0"/>
		<edge from-layer="48" from-port="3" to-layer="49" to-port="0"/>
		<edge from-layer="49" from-port="1" to-layer="50" to-port="0"/>
		<edge from-layer="50" from-port="3" to-layer="51" to-port="0"/>
		<edge from-layer="51" from-port="1" to-layer="52" to-port="0"/>
		<edge from-layer="52" from-port="3" to-layer="53" to-port="0"/>
		<edge from-layer="53" from-port="3" to-layer="54" to-port="0"/>
		<edge from-layer="54" from-port="1" to-layer="55" to-port="0"/>
		<edge from-layer="55" from-port="3" to-layer="56" to-port="0"/>
		<edge from-layer="56" from-port="1" to-layer="57" to-port="0"/>
		<edge from-layer="52" from-port="3" to-layer="58" to-port="0"/>
		<edge from-layer="57" from-port="3" to-layer="58" to-port="1"/>
		<edge from-layer="58" from-port="2" to-layer="59" to-port="0"/>
		<edge from-layer="59" from-port="3" to-layer="60" to-port="0"/>
		<edge from-layer="60" from-port="1" to-layer="61" to-port="0"/>
		<edge from-layer="61" from-port="3" to-layer="62" to-port="0"/>
		<edge from-layer="62" from-port="1" to-layer="63" to-port="0"/>
		<edge from-layer="58" from-port="2" to-layer="64" to-port="0"/>
		<edge from-layer="63" from-port="3" to-layer="64" to-port="1"/>
		<edge from-layer="64" from-port="2" to-layer="65" to-port="0"/>
		<edge from-layer="65" from-port="3" to-layer="66" to-port="0"/>
		<edge from-layer="66" from-port="1" to-layer="67" to-port="0"/>
		<edge from-layer="67" from-port="3" to-layer="68" to-port="0"/>
		<edge from-layer="68" from-port="1" to-layer="69" to-port="0"/>
		<edge from-layer="64" from-port="2" to-layer="70" to-port="0"/>
		<edge from-layer="69" from-port="3" to-layer="70" to-port="1"/>
		<edge from-layer="70" from-port="2" to-layer="71" to-port="0"/>
		<edge from-layer="71" from-port="3" to-layer="72" to-port="0"/>
		<edge from-layer="72" from-port="1" to-layer="73" to-port="0"/>
		<edge from-layer="73" from-port="3" to-layer="74" to-port="0"/>
		<edge from-layer="74" from-port="1" to-layer="75" to-port="0"/>
		<edge from-layer="75" from-port="3" to-layer="76" to-port="0"/>
		<edge from-layer="76" from-port="3" to-layer="77" to-port="0"/>
		<edge from-layer="77" from-port="1" to-layer="78" to-port="0"/>
		<edge from-layer="78" from-port="3" to-layer="79" to-port="0"/>
		<edge from-layer="79" from-port="1" to-layer="80" to-port="0"/>
		<edge from-layer="75" from-port="3" to-layer="81" to-port="0"/>
		<edge from-layer="80" from-port="3" to-layer="81" to-port="1"/>
		<edge from-layer="81" from-port="2" to-layer="82" to-port="0"/>
		<edge from-layer="82" from-port="3" to-layer="83" to-port="0"/>
		<edge from-layer="83" from-port="1" to-layer="84" to-port="0"/>
		<edge from-layer="84" from-port="3" to-layer="85" to-port="0"/>
		<edge from-layer="85" from-port="1" to-layer="86" to-port="0"/>
		<edge from-layer="81" from-port="2" to-layer="87" to-port="0"/>
		<edge from-layer="86" from-port="3" to-layer="87" to-port="1"/>
		<edge from-layer="87" from-port="2" to-layer="88" to-port="0"/>
		<edge from-layer="88" from-port="3" to-layer="89" to-port="0"/>
		<edge from-layer="89" from-port="1" to-layer="90" to-port="0"/>
		<edge from-layer="90" from-port="3" to-layer="91" to-port="0"/>
		<edge from-layer="91" from-port="1" to-layer="92" to-port="0"/>
		<edge from-layer="92" from-port="3" to-layer="93" to-port="0"/>
		<edge from-layer="93" from-port="3" to-layer="94" to-port="0"/>
		<edge from-layer="94" from-port="1" to-layer="95" to-port="0"/>
		<edge from-layer="95" from-port="3" to-layer="96" to-port="0"/>
		<edge from-layer="96" from-port="1" to-layer="97" to-port="0"/>
		<edge from-layer="92" from-port="3" to-layer="98" to-port="0"/>
		<edge from-layer="97" from-port="3" to-layer="98" to-port="1"/>
		<edge from-layer="98" from-port="2" to-layer="99" to-port="0"/>
		<edge from-layer="99" from-port="3" to-layer="100" to-port="0"/>
		<edge from-layer="100" from-port="1" to-layer="101" to-port="0"/>
		<edge from-layer="101" from-port="3" to-layer="102" to-port="0"/>
		<edge from-layer="102" from-port="1" to-layer="103" to-port="0"/>
		<edge from-layer="98" from-port="2" to-layer="104" to-port="0"/>
		<edge from-layer="103" from-port="3" to-layer="104" to-port="1"/>
		<edge from-layer="104" from-port="2" to-layer="105" to-port="0"/>
		<edge from-layer="105" from-port="3" to-layer="106" to-port="0"/>
		<edge from-layer="106" from-port="1" to-layer="107" to-port="0"/>
		<edge from-layer="107" from-port="3" to-layer="108" to-port="0"/>
		<edge from-layer="108" from-port="1" to-layer="109" to-port="0"/>
		<edge from-layer="109" from-port="3" to-layer="110" to-port="0"/>
		<edge from-layer="110" from-port="1" to-layer="111" to-port="0"/>
		<edge from-layer="111" from-port="3" to-layer="112" to-port="0"/>
		<edge from-layer="112" from-port="1" to-layer="113" to-port="0"/>
		<edge from-layer="109" from-port="3" to-layer="114" to-port="0"/>
		<edge from-layer="114" from-port="3" to-layer="115" to-port="0"/>
		<edge from-layer="113" from-port="1" to-layer="116" to-port="0"/>
		<edge from-layer="115" from-port="1" to-layer="116" to-port="1"/>
		<edge from-layer="116" from-port="2" to-layer="117" to-port="0"/>
		<edge from-layer="117" from-port="3" to-layer="118" to-port="0"/>
		<edge from-layer="118" from-port="1" to-layer="119" to-port="0"/>
		<edge from-layer="119" from-port="3" to-layer="120" to-port="0"/>
		<edge from-layer="120" from-port="1" to-layer="121" to-port="0"/>
		<edge from-layer="121" from-port="1" to-layer="122" to-port="0"/>
		<edge from-layer="122" from-port="1" to-layer="123" to-port="0"/>
		<edge from-layer="123" from-port="1" to-layer="125" to-port="0"/>
		<edge from-layer="124" from-port="1" to-layer="125" to-port="1"/>
	</edges>
	<meta_data>
		<MO_version value="2019.3.0-408-gac8584cb7"/>
		<cli_parameters>
			<blobs_as_inputs value="False"/>
			<caffe_parser_path value="DIR"/>
			<data_type value="float"/>
			<disable_nhwc_to_nchw value="False"/>
			<disable_omitting_optional value="False"/>
			<disable_resnet_optimization value="False"/>
			<enable_concat_optimization value="False"/>
			<enable_flattening_nested_params value="False"/>
			<enable_ssd_gluoncv value="False"/>
			<extensions value="DIR"/>
			<framework value="tf"/>
			<freeze_placeholder_with_value value="{}"/>
			<generate_experimental_IR_V10 value="False"/>
			<input value="ImageTensor"/>
			<input_model value="DIR/frozen_inference_graph.pb"/>
			<input_model_is_text value="False"/>
			<input_shape value="(1,513,513,3)"/>
			<k value="DIR/CustomLayersMapping.xml"/>
			<keep_quantize_ops_in_IR value="False"/>
			<keep_shape_ops value="False"/>
			<legacy_mxnet_model value="False"/>
			<log_level value="ERROR"/>
			<mean_scale_values value="{}"/>
			<mean_values value="()"/>
			<move_to_preprocess value="False"/>
			<output value="['SemanticPredictions']"/>
			<output_dir value="DIR"/>
			<placeholder_shapes value="{'ImageTensor': array([  1, 513, 513,   3])}"/>
			<remove_memory value="False"/>
			<remove_output_softmax value="False"/>
			<reverse_input_channels value="False"/>
			<save_params_from_nd value="False"/>
			<scale_values value="()"/>
			<silent value="False"/>
			<steps value="False"/>
			<version value="False"/>
			<unset unset_cli_parameters="batch, counts, disable_fusing, disable_gfusing, finegrain_fusing, generate_deprecated_IR_V2, input_checkpoint, input_meta_graph, input_proto, input_symbol, mean_file, mean_file_offsets, model_name, nd_prefix_name, pretrained_model_name, saved_model_dir, saved_model_tags, scale, tensorboard_logdir, tensorflow_custom_layer_libraries, tensorflow_custom_operations_config_update, tensorflow_object_detection_api_pipeline_config, tensorflow_operation_patterns, tensorflow_subgraph_patterns, tensorflow_use_custom_operations_config"/>
		</cli_parameters>
	</meta_data>
</net>
