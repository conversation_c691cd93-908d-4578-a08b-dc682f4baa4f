apply plugin: 'com.android.application'

android {
    compileSdkVersion 26
    buildToolsVersion "27.0.3"
    defaultConfig {
        applicationId "android.example.com.tflitecamerademo3"
        // Required by Camera2 API.
        minSdkVersion 21
        targetSdkVersion 26
        versionCode 1
        versionName "1.0"

        renderscriptTargetApi 24
        renderscriptSupportModeEnabled true
    }
    lintOptions {
        abortOnError false
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    aaptOptions {
        noCompress "tflite"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    maven {
        url 'https://google.bintray.com/tensorflow'
    }
}

allprojects {
    repositories {
        // Uncomment if you want to use a local repo.
        // mavenLocal()
        jcenter()
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:25.2.0'
    implementation 'com.android.support.constraint:constraint-layout:1.0.2'
    implementation 'com.android.support:design:25.2.0'
    implementation 'com.android.support:support-annotations:25.3.1'
    implementation 'com.android.support:support-v13:25.2.0'

    // Build off of nightly TensorFlow Lite
    //implementation 'org.tensorflow:tensorflow-lite:0.0.0-nightly'
    compile group: 'org.tensorflow', name: 'tensorflow-lite', version: '1.15.0'
    //implementation 'org.tensorflow:tensorflow-lite-gpu:0.0.0-nightly'
    compile group: 'org.tensorflow', name: 'tensorflow-lite-gpu', version: '1.15.0'
    implementation 'com.quickbirdstudios:opencv:4.1.0'
    implementation 'jp.co.cyberagent.android:gpuimage:2.0.3'
    // Use local TensorFlow library
    // implementation 'org.tensorflow:tensorflow-lite-local:0.0.0'
}

