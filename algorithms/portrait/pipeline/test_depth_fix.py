#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the depth processing fixes
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_raw import Simplified<PERSON><PERSON><PERSON>tPipeline

def test_depth_processing_fix():
    """Test the fixed depth processing with problematic image"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/p12.jpg"
    output_dir = "/home/<USER>/T2/data/depth_fix_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        print("Please update the test_image path in the script")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*60)
    print("Testing Fixed Depth Processing")
    print("="*60)
    
    # Test different configurations that should now work
    test_configs = [
        {
            'name': 'robust_normalization',
            'config': {
                'normalize_depth': True,
                'depth_min_percentile': 5,
                'depth_max_percentile': 95,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            }
        },
        {
            'name': 'preserve_depth_fixed',
            'config': {
                'preserve_depth_range': True,
                'normalize_depth': False,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            }
        },
        {
            'name': 'conservative_settings',
            'config': {
                'normalize_depth': True,
                'depth_min_percentile': 10,
                'depth_max_percentile': 90,
                'K': 40.0,  # Lower blur strength
                'gamma': 3.0,  # Lower gamma
                'defocus_scale': 15.0  # Higher defocus scale for smaller values
            }
        }
    ]
    
    for test_config in test_configs:
        print(f"\nTesting: {test_config['name']}")
        print("-" * 40)
        
        try:
            # Initialize pipeline with specific config
            pipeline = SimplifiedPortraitPipeline(bokeh_config=test_config['config'])
            
            # Process image
            output_path = os.path.join(output_dir, f"bokeh_{test_config['name']}.jpg")
            intermediate_dir = os.path.join(output_dir, f"intermediates_{test_config['name']}")
            
            result = pipeline.process_image(
                test_image,
                output_path,
                save_intermediates=True,
                intermediate_dir=intermediate_dir
            )
            
            print(f"✓ SUCCESS: {output_path}")
            print(f"✓ Time: {result['processing_time']:.2f}s")
            print(f"✓ Focus depth: {result['focus_depth']:.3f}")
            print(f"✓ Raw depth range: [{result['raw_depth_map'].min():.3f}, {result['raw_depth_map'].max():.3f}]")
            print(f"✓ Processed depth range: [{result['processed_depth_map'].min():.3f}, {result['processed_depth_map'].max():.3f}]")
            
            if result['face_box'] is not None:
                print(f"✓ Face detected: {result['face_box']}")
            else:
                print("✓ No face detected (using portrait/center region)")
                
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")
            import traceback
            traceback.print_exc()

def test_extreme_depth_values():
    """Test with manually created extreme depth values"""
    
    import numpy as np
    import cv2
    
    output_dir = "/home/<USER>/T2/data/extreme_depth_test"
    os.makedirs(output_dir, exist_ok=True)
    
    print("\n" + "="*60)
    print("Testing Extreme Depth Values")
    print("="*60)
    
    # Test different extreme depth scenarios
    extreme_tests = [
        {
            'name': 'very_large_values',
            'depth_range': (0, 1000),
            'description': 'Depth values from 0 to 1000'
        },
        {
            'name': 'very_small_values',
            'depth_range': (0, 0.001),
            'description': 'Depth values from 0 to 0.001'
        },
        {
            'name': 'negative_values',
            'depth_range': (-100, 100),
            'description': 'Depth values from -100 to 100'
        }
    ]
    
    for test in extreme_tests:
        print(f"\nTesting: {test['name']}")
        print(f"Description: {test['description']}")
        print("-" * 40)
        
        try:
            # Create synthetic depth map
            depth_min, depth_max = test['depth_range']
            synthetic_depth = np.random.uniform(depth_min, depth_max, (400, 600)).astype(np.float32)
            
            # Initialize pipeline
            pipeline = SimplifiedPortraitPipeline()
            
            # Test depth processing directly
            processed_depth = pipeline.process_depth_map(synthetic_depth)
            
            print(f"✓ Input depth range: [{synthetic_depth.min():.6f}, {synthetic_depth.max():.6f}]")
            print(f"✓ Processed depth range: [{processed_depth.min():.6f}, {processed_depth.max():.6f}]")
            print(f"✓ All values in [0,1]: {np.all((processed_depth >= 0) & (processed_depth <= 1))}")
            
            # Test focus calculation
            focus_depth = pipeline.calculate_focus_depth(processed_depth)
            print(f"✓ Focus depth: {focus_depth:.6f}")
            print(f"✓ Focus in valid range: {0 <= focus_depth <= 1}")
            
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")

def test_defocus_calculation():
    """Test defocus calculation with various parameters"""
    
    print("\n" + "="*60)
    print("Testing Defocus Calculation")
    print("="*60)
    
    import numpy as np
    
    # Test depth map (normalized 0-1)
    depth_map = np.random.uniform(0, 1, (100, 100)).astype(np.float32)
    focus_depth = 0.5
    
    # Test different parameter combinations
    param_tests = [
        {'K': 20, 'defocus_scale': 10, 'expected_max': 10},
        {'K': 60, 'defocus_scale': 10, 'expected_max': 30},
        {'K': 100, 'defocus_scale': 15, 'expected_max': 33},
    ]
    
    for params in param_tests:
        K = params['K']
        defocus_scale = params['defocus_scale']
        
        # Calculate defocus
        defocus = K * (depth_map - focus_depth) / defocus_scale
        
        # Apply clamping (as in the fixed code)
        max_defocus = 50.0
        defocus_clamped = np.clip(defocus, -max_defocus, max_defocus)
        
        print(f"K={K}, defocus_scale={defocus_scale}:")
        print(f"  Raw defocus range: [{defocus.min():.3f}, {defocus.max():.3f}]")
        print(f"  Clamped defocus range: [{defocus_clamped.min():.3f}, {defocus_clamped.max():.3f}]")
        print(f"  Values clamped: {np.any(np.abs(defocus) > max_defocus)}")
        print()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Depth Processing Fixes')
    parser.add_argument('--test', choices=['depth', 'extreme', 'defocus', 'all'], 
                       default='all', help='Which test to run')
    
    args = parser.parse_args()
    
    if args.test in ['depth', 'all']:
        test_depth_processing_fix()
    
    if args.test in ['extreme', 'all']:
        test_extreme_depth_values()
    
    if args.test in ['defocus', 'all']:
        test_defocus_calculation()
    
    print("\n" + "="*60)
    print("All tests completed!")
    print("="*60)
