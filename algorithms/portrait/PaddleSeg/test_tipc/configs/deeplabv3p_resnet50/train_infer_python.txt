===========================train_params===========================
model_name:deeplabv3p_resnet50
python:python3.7
gpu_list:0|0,1
Global.use_gpu:null|null
--precision:null
--iters:lite_train_lite_infer=20|lite_train_whole_infer=20|whole_train_whole_infer=1000
--save_dir:
--batch_size:lite_train_lite_infer=2|lite_train_whole_infer=2|whole_train_whole_infer=8
--model_path:null
train_model_name:best_model/model.pdparams
train_infer_img_dir:test_tipc/data/mini_supervisely/test.txt
--profiler_options:null
##
trainer:norm
norm_train:train.py --config test_tipc/configs/deeplabv3p_resnet50/deeplabv3p_resnet50_humanseg_512x512_mini_supervisely.yml --do_eval --save_interval 500 --seed 100
pact_train:null
fpgm_train:null
distill_train:null
null:null
null:null
##
===========================eval_params===========================
eval:null
null:null
##
===========================export_params===========================
--save_dir:
--model_path:
norm_export:export.py --config test_tipc/configs/deeplabv3p_resnet50/deeplabv3p_resnet50_humanseg_512x512_mini_supervisely.yml
quant_export:null
fpgm_export:null
distill_export:null
export1:null
export2:null
===========================infer_params===========================
infer_model:./test_tipc/output/deeplabv3p_resnet50/deeplabv3p_resnet50_os8_humanseg_512x512_100k/model.pdparams
infer_export:export.py --config test_tipc/configs/deeplabv3p_resnet50/deeplabv3p_resnet50_humanseg_512x512_mini_supervisely.yml
infer_quant:False
inference:deploy/python/infer.py
--device:cpu|gpu
--enable_mkldnn:True
--cpu_threads:6
--batch_size:1
--use_trt:False
--precision:fp32
--config:
--image_path:./test_tipc/data/mini_supervisely/test.txt
--save_log_path:null
--benchmark:True
--save_dir:
--model_name:deeplabv3p_resnet50
===========================infer_benchmark_params==========================
random_infer_input:[{float32,[3,512,512]}]
===========================train_benchmark_params==========================
batch_size:2
fp_items:fp32|fp16
epoch:150
--profiler_options:'batch_range=[10,20];state=GPU;tracer_option=Default;profile_path=model.profile'  
flags:FLAGS_eager_delete_tensor_gb=0.0;FLAGS_fraction_of_gpu_memory_to_use=0.98;FLAGS_conv_workspace_size_limit=4096;FLAGS_cudnn_deterministic=False
log_iters:5