_base_: '../_base_/cityscapes.yml'

model:
  type: DecoupledSegNet
  backbone:
    type: ResNet50_vd
    output_stride: 8
    multi_grid: [1, 2, 4]
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet50_vd_ssld_v2.tar.gz
  num_classes: 19
  backbone_indices: [0, 3]
  aspp_ratios: [1, 12, 24, 36]
  aspp_out_channels: 256
  align_corners: False
  pretrained: null

loss:
  types:
    - type: OhemCrossEntropyLoss
    - type: RelaxBoundaryLoss
    - type: BCELoss
      weight: 'dynamic'
      edge_label: True
    - type: OhemEdgeAttentionLoss
  coef: [1,1,25,1]

train_dataset:
  edge: True
