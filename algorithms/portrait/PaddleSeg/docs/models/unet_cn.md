# U-Net

U-Net [1] 起源于医疗图像分割，具有参数少、计算快、应用性强的特点，对于一般场景适应度很高。U-Net最早于2015年提出，并在ISBI 2015 Cell Tracking Challenge取得了第一。经过发展，目前有多个变形和应用。
原始U-Net的结构是标准的编码器-解码器结构。如下图所示，左侧可视为一个编码器，右侧可视为一个解码器。编码器由四个子模块组成，每个子模块包含两个卷积层，每个子模块之后又通过max pool进行下采样。编码器整体呈现逐渐缩小的结构，不断减少池化层的空间维度，缩小特征图的分辨率，以捕获上下文信息。
解码器呈现与编码器对称的扩张结构，逐步修复分割对象的细节和空间维度，实现精准的定位。解码器同样也包含四个子模块，分辨率通过上采样操作依次增大，直到与输入图像的分辨率基本一致。
该网络还使用了跳跃连接，即解码器每上采样一次，就以拼接的方式将解码器和编码器中对应相同分辨率的特征图进行特征融合，帮助解码器更好地恢复目标的细节。由于网络整体结构类似于大写的英文字母U，故得名U-Net。

![](./images/UNet.png)

<div align = "center">U-Net结构图</div>

具体原理细节请参考[U-Net:Convolutional Networks for Biomedical Image Segmentation](https://arxiv.org/abs/1505.04597)。
