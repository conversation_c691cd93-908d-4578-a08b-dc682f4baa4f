{"nbformat": 4, "nbformat_minor": 0, "metadata": {"accelerator": "GPU", "colab": {"name": "modnet_video.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "n1HvLpY25-_g"}, "source": ["MODNet is a light-weight **matting** objective decomposition network (MODNet), which can process portrait matting from a single input image in **real time**. The trimap-free model can run at **60 fps** on a GPU and achieves remarkable results in daily **photos and videos**. In this demo we will use the official pretrained **models** and convert them to **onnx and coreml** formats.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "EiEhkr-kk1dO"}, "source": ["Install **pytorch** v1.6.0"]}, {"cell_type": "code", "metadata": {"id": "oca-pcFLki0g"}, "source": ["!pip install torch==1.6.0+cu101 torchvision==0.7.0+cu101 -f https://download.pytorch.org/whl/torch_stable.html"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "dmihKW1VmG9u"}, "source": ["Clone the **MODNet** repository and download the pretrained checkpoints, along with samples."]}, {"cell_type": "code", "metadata": {"id": "p0rg3wNdUF3W"}, "source": ["import os\n", "\n", "# Download sample videos and images\n", "!wget https://www.dropbox.com/s/1nck2tvmbjtlt28/data_modnet.zip\n", "!unzip data_modnet.zip\n", "\n", "# clone the MODNet github repository\n", "%cd /content\n", "if not os.path.exists('MODNet'):\n", "  !git clone https://github.com/ZHKKKe/MODNet\n", "%cd MODNet/\n", "\n", "# Download the pre-trained ckpt for video and image matting\n", "pretrained_ckpt = 'pretrained/modnet_webcam_portrait_matting.ckpt'\n", "if not os.path.exists(pretrained_ckpt):\n", "  !gdown --id 1Nf1ZxeJZJL8Qx9KadcYYyEmmlKhTADxX \\\n", "          -O pretrained/modnet_webcam_portrait_matting.ckpt\n", "  !gdown --id 1mcr7ALciuAsHCpLnrtG_eop5-EYhbCmz \\\n", "          -O pretrained/modnet_photographic_portrait_matting.ckpt"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "zXVPj2VgUd0a"}, "source": ["To convert the original **pretrained** model to onnx format, **modify** the original network as follows:-\n", "\n", "* In the modnet architecture set the argument **inference=True** as default value, in forward functions of LR, HR and MODNet modules.\n", "\n", "* In the MODNet module, return only the **pred_matte** in the forward function and remove other tensors(outputs)."]}, {"cell_type": "markdown", "metadata": {"id": "IZaKyJiSr62T"}, "source": ["Choose the pytorch model for **portrait image matting**."]}, {"cell_type": "code", "metadata": {"id": "jlUzmLboRPRw"}, "source": ["%cd /content/MODNet/\n", "pretrained_ckpt = 'pretrained/modnet_photographic_portrait_matting.ckpt'"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "tVFabBdKrwnn"}, "source": ["Convert the pytorch model to **onnx** format."]}, {"cell_type": "code", "metadata": {"id": "lDONLUvAUgzo"}, "source": ["import io\n", "import PIL\n", "import numpy as np\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.transforms as transforms\n", "\n", "from src.models.modnet import MODNet\n", "\n", "model = MODNet(backbone_pretrained=False)\n", "model = nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(model).cuda()\n", "\n", "state_dict = torch.load(pretrained_ckpt)\n", "model.load_state_dict(state_dict)\n", "model.eval()\n", "\n", "dummy_input = torch.randn(1, 3, 512, 512).cuda()\n", "torch.onnx.export(model.module, dummy_input, '/content/modnet_image.onnx', export_params = True, opset_version=12, do_constant_folding=True, verbose=True, keep_initializers_as_inputs=True)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "u-ilAgaZqsKJ"}, "source": ["Install **onnx-simplifier** and optimize the onnx model."]}, {"cell_type": "code", "metadata": {"id": "KfQBziVyW7za"}, "source": ["!pip install onnx-simplifier\n", "!python3 -m onnxsim /content/modnet_image.onnx /content/modnet_image_optim.onnx"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "AJOQ-8syq0je"}, "source": ["Perform inference on single image, using **onnx-runtime**."]}, {"cell_type": "code", "metadata": {"id": "U6tDp8p6YFsq"}, "source": ["import numpy as np\n", "import cv2\n", "import onnxruntime as rt\n", "from PIL import Image\n", "\n", "inp = Image.open('/content/portrait.jpg')\n", "inp = inp.resize((512, 512), Image.ANTIALIAS)\n", "inp = np.asarray(inp)/255.0\n", "\n", "# Preprocess images based on the original training/inference code\n", "mean = [0.5, 0.5,  0.5 ]\n", "std = [0.5,  0.5, 0.5]\n", "\n", "img = (inp-mean)/std\n", "\n", "img = img.transpose((2, 0, 1))\n", "img = img[np.newaxis,...]\n", "\n", "# Perform inference using the ONNX runtime\n", "sess = rt.InferenceSession(\"/content/modnet_image_optim.onnx\")\n", "input_name = sess.get_inputs()[0].name\n", "pred_onnx = sess.run(None, {input_name: img.astype(np.float32)})[0]\n", "res = pred_onnx"], "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "EM0gpAm7qNRG"}, "source": ["Plot the results using **matplotlib**."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 286}, "id": "s6lzLj0__ofE", "outputId": "edf1b4ef-d809-469d-9aee-4f96709d4b9c"}, "source": ["from matplotlib import pyplot as plt\n", "\n", "mask = cv2.cvtColor(res.squeeze(),cv2.COLOR_GRAY2RGB)\n", "crop = inp * mask\n", "comb = np.hstack([inp, mask, crop])\n", "\n", "plt.figure(figsize = (20,4))\n", "plt.imshow(comb)"], "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f5c2278d748>"]}, "metadata": {"tags": []}, "execution_count": 7}, {"output_type": "display_data", "data": {"image/png": "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*********************************************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\n", "text/plain": ["<Figure size 1440x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "RyZJxrzerBxg"}, "source": ["**Note:** In an ideal use case, you may need to resize the input image based on **aspect ratio** and rescale the output back to the original resolution."]}, {"cell_type": "markdown", "metadata": {"id": "rhgFAz37SCH6"}, "source": ["**Video Segmentation**"]}, {"cell_type": "markdown", "metadata": {"id": "TO4kC9N9xGpt"}, "source": ["Choose the pytorch model for **webcam portrait matting**."]}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EA57Uk3TwkGc", "outputId": "2a75e47b-318c-4b74-f042-b01bbdcdce5f"}, "source": ["%cd /content/MODNet/\n", "pretrained_ckpt = 'pretrained/modnet_webcam_portrait_matting.ckpt'"], "execution_count": 8, "outputs": [{"output_type": "stream", "text": ["/content/MODNet\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "iEXJvdhLxWMK"}, "source": ["Convert the pytorch model to **onnx** format."]}, {"cell_type": "code", "metadata": {"id": "2E8AwpGawm_Y"}, "source": ["import io\n", "import PIL\n", "import numpy as np\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.transforms as transforms\n", "\n", "from src.models.modnet import MODNet\n", "\n", "model = MODNet(backbone_pretrained=False)\n", "model = nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(model).cuda()\n", "\n", "state_dict = torch.load(pretrained_ckpt)\n", "model.load_state_dict(state_dict)\n", "model.eval()\n", "\n", "dummy_input = torch.randn(1, 3, 512, 512).cuda()\n", "torch.onnx.export(model.module, dummy_input, '/content/modnet_video.onnx', export_params = True, opset_version=12, do_constant_folding=True, verbose=True, keep_initializers_as_inputs=True)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "sXH6OrrFw6yD"}, "source": ["**Optimize** the onnx model using onnx-simplifier."]}, {"cell_type": "code", "metadata": {"id": "QSS_IPzNwrXw"}, "source": ["!python3 -m onnxsim /content/modnet_video.onnx /content/modnet_video_optim.onnx"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "CnysFdlhyPe6"}, "source": ["Perform inference on video input, using **onnx-runtime**."]}, {"cell_type": "code", "metadata": {"id": "1Gprb-AnWlVu"}, "source": ["import numpy as np\n", "import cv2\n", "import torch\n", "\n", "# Input video file\n", "video_input = \"/content/pexel_video.mp4\"\n", "target_size =  (512,512)\n", "\n", "# Video capturer and writer\n", "vidcap = cv2.VideoCapture(video_input)\n", "vidwrt = cv2.VideoWriter('result.mp4', cv2.VideoWriter_fourcc(*'MJPG'), 20, target_size)\n", "\n", "# Load and resize the background image\n", "bgd = cv2.imread('/content/manhattan.jpg')\n", "bgd = cv2.resize(bgd, (512,512))\n", "\n", "# Mean and std. deviation for normalization\n", "mean = [0.5, 0.5, 0.5 ]\n", "std = [0.5,  0.5, 0.5]\n", "\n", "# Initialize onnx runtime session\n", "sess = rt.InferenceSession(\"/content/modnet_video_optim.onnx\")\n", "input_name = sess.get_inputs()[0].name\n", "\n", "# Initialize frame counter\n", "frame_count = 0\n", "\n", "while True:\n", "\n", "    # Read input frames\n", "    success, frame = vidcap.read()\n", "    if not success:\n", "           vidcap.release()\n", "           vidwrt.release()\n", "           break\n", "\n", "    frame_count = frame_count + 1\n", "    if frame_count%10 == 0:\n", "      print(\"Processing frame: \"+ str(frame_count))\n", "\n", "    # Resize the input video frame\n", "    frame = cv2.resize(frame, target_size, cv2.INTER_AREA)\n", "    \n", "    # Converet frame to RGB format\n", "    img =  cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "        \n", "    # Normalize and add batch dimension\n", "    img = img /255.0\n", "    img = (img-mean)/std\n", "    img = img.transpose((2, 0, 1))\n", "    img = img[np.newaxis,...]\n", "\n", "\n", "    # Perform inference using the ONNX runtime\n", "    pred_onnx = sess.run(None, {input_name: img.astype(np.float32)})[0]\n", "    alpha_matte=pred_onnx.squeeze() # 512x512\n", "\n", "      \n", "    # Alpha blending with background image\n", "    mask = alpha_matte[..., np.newaxis]\n", "    blend = (frame * mask) + (bgd * (1-mask))\n", "\n", "    # Write the frames to a video file\n", "    vidwrt.write(np.uint8(blend))"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "NSJ0uWAuyfDT"}, "source": ["**Note:** In an ideal use case, you may need to resize the input frame based on **aspect ratio** and rescale the output back to the original resolution."]}, {"cell_type": "markdown", "metadata": {"id": "WDMEUqldTVvA"}, "source": ["Display the **output** video on colaboratory"]}, {"cell_type": "code", "metadata": {"id": "FaJQCZ7tTUpH"}, "source": ["from IPython.display import HTML\n", "from base64 import b64encode\n", "\n", "# Input and compresed video paths\n", "save_path = \"/content/MODNet/result.mp4\"\n", "compressed_path = \"/content/MODNet/result_compressed.mp4\"\n", "\n", "# Compress the video using ffmpeg\n", "os.system(f\"ffmpeg -i {save_path} -vcodec libx264 {compressed_path}\")\n", "\n", "# Show the output video\n", "mp4 = open(compressed_path,'rb').read()\n", "data_url = \"data:video/mp4;base64,\" + b64encode(mp4).decode()\n", "HTML(\"\"\"\n", "<video width=400 controls>\n", "      <source src=\"%s\" type=\"video/mp4\">\n", "</video>\n", "\"\"\" % data_url)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "KijB7lchccu-"}, "source": ["**Core ML Conversion**"]}, {"cell_type": "markdown", "metadata": {"id": "_BspHPETrBHB"}, "source": ["Install latest **coremltools**."]}, {"cell_type": "code", "metadata": {"id": "WHHm-1f6dP4Q"}, "source": ["!pip install --upgrade coremltools"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "SnIvYXAJq__S"}, "source": ["Choose the pytorch model for **portrait image matting**."]}, {"cell_type": "code", "metadata": {"id": "2UGKBLC6eaj0"}, "source": ["%cd /content/MODNet/\n", "pretrained_ckpt = 'pretrained/modnet_photographic_portrait_matting.ckpt'"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "U6SAUMberZur"}, "source": ["Convert the pytorch model to **coreml**."]}, {"cell_type": "code", "metadata": {"id": "YkNOnZl4caO8"}, "source": ["import coremltools as ct\n", "from src.models.modnet import MODNet\n", "\n", "\n", "model = MODNet(backbone_pretrained=False)\n", "model = nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(model).cuda()\n", "\n", "state_dict = torch.load(pretrained_ckpt)\n", "model.load_state_dict(state_dict)\n", "model.eval()\n", "\n", "# Get a pytorch model and save it as a *.pt file\n", "pytorch_model = model.module\n", "pytorch_model.eval()\n", "example_input = torch.rand(1, 3, 512, 512).cuda()\n", "traced_model = torch.jit.trace(pytorch_model, example_input)\n", "traced_model.save(\"modnet_image.pt\")\n", "\n", "# Convert the saved PyTorch model to Core ML\n", "mlmodel = ct.convert(\"modnet_image.pt\",\n", "                    inputs=[ct.TensorType(shape=(1, 3, 512, 512))])\n", "\n", "# Save the coreml model\n", "mlmodel.save(\"modnet_image.mlmodel\")"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "koCWQEcMrG--"}, "source": ["Choose the pytorch model for **portrait video matting**."]}, {"cell_type": "code", "metadata": {"id": "JTdP-aHWrMmU"}, "source": ["%cd /content/MODNet/\n", "pretrained_ckpt = 'pretrained/modnet_webcam_portrait_matting.ckpt'"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ViSTHPjOrsHv"}, "source": ["Convert the pytorch model to **coreml**."]}, {"cell_type": "code", "metadata": {"id": "qMci75rdrOqi"}, "source": ["import coremltools as ct\n", "from src.models.modnet import MODNet\n", "\n", "model = MODNet(backbone_pretrained=False)\n", "model = nn.<PERSON><PERSON><PERSON><PERSON><PERSON>(model).cuda()\n", "\n", "state_dict = torch.load(pretrained_ckpt)\n", "model.load_state_dict(state_dict)\n", "model.eval()\n", "\n", "# Get a pytorch model and save it as a *.pt file\n", "pytorch_model = model.module\n", "pytorch_model.eval()\n", "example_input = torch.rand(1, 3, 512, 512).cuda()\n", "traced_model = torch.jit.trace(pytorch_model, example_input)\n", "traced_model.save(\"modnet_video.pt\")\n", "\n", "# Convert the saved PyTorch model to Core ML\n", "mlmodel = ct.convert(\"modnet_video.pt\",\n", "                    inputs=[ct.TensorType(shape=(1, 3, 512, 512))])\n", "\n", "# Save the coreml model\n", "mlmodel.save(\"modnet_video.mlmodel\")"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Q5HpnuVmtW-q"}, "source": ["**ONNX2Tensorflow Conversion Issues**"]}, {"cell_type": "markdown", "metadata": {"id": "NZRM8ZpLGmax"}, "source": ["Convert the onnx model to tensorflow using **onnx-tensorflow**."]}, {"cell_type": "code", "metadata": {"id": "3ZfAoa2LtTkq"}, "source": ["!git clone https://github.com/onnx/onnx-tensorflow.git\n", "%cd onnx-tensorflow\n", "!pip install -e .\n", "!onnx-tf convert -i /content/modnet_image_optim.onnx -o /content/tfmodel"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "6vRgwtUgEsOk"}, "source": ["**Issues:-** RuntimeError - Resize coordinate_transformation_mode=pytorch_half_pixel is not supported in Tensorflow."]}, {"cell_type": "markdown", "metadata": {"id": "rf5JaoQlGuzz"}, "source": ["\n", "Convert the onnx model to keras using **onnx2keras**."]}, {"cell_type": "code", "metadata": {"id": "EaJAkOr3DoKW"}, "source": ["!pip install onnx2keras\n", "\n", "import onnx\n", "from onnx2keras import onnx_to_keras\n", "\n", "# Load ONNX model\n", "onnx_model = onnx.load('/content/modnet_image_optim.onnx')\n", "onnx.checker.check_model(onnx_model)\n", "print('The model is checked!')\n", "\n", "# Call the converter\n", "k_model = onnx_to_keras(onnx_model, ['input.1'], change_ordering=True )"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "49y0Xs4KFDNR"}, "source": ["**Issues:**- KeyError- 'min' and/or KeyError: 'Resize'"]}, {"cell_type": "markdown", "metadata": {"id": "AJXKCQTFGDi9"}, "source": ["The original model was trained in pytorch with **bilinear upsample and align corners=False**.\n", "\n", "`F.interpolate(..., scale_factor=2, mode='bilinear', align_corners=False)`\n", "\n", "It will create a resize layer with resize attribute **pytorch_halfpixel**. Currently, it looks like this behaviour is not supported by **tensorflow/keras**.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "FnHaCrS_2aBc"}, "source": ["**References:-**\n", "\n", "\n", "*   https://github.com/ZHKKKe/MODNet\n", "*   https://github.com/onnx/onnx-tensorflow\n", "*   https://github.com/nerox8664/onnx2keras\n", "*   https://github.com/apple/coremltools/issues/1012\n", "*   https://towardsdatascience.com/yolov3-pytorch-on-google-colab-c4a79eeecdea\n", "*   https://github.com/daquexian/onnx-simplifier\n", "*   https://coremltools.readme.io/docs/introductory-quickstart\n", "*   https://github.com/onnx/onnx/blob/master/docs/PythonAPIOverview.md\n", "*   https://github.com/microsoft/onnxruntime/blob/master/docs/python/tutorial.rst#step-3-load-and-run-the-model-using-onnx-runtime\n", "\n"]}]}