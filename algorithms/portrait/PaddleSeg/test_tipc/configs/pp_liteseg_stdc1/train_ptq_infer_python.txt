===========================ptq_params===========================
model_name:pp_liteseg_stdc1_KL
python:python3.7
##
--model_dir:test_tipc/output/pp_liteseg_stdc1_KL/pp_liteseg_infer_model
##
--config:test_tipc/configs/pp_liteseg_stdc1/pp_liteseg_stdc1_cityscapes_1024x512_160k.yml
--batch_num:1
--batch_size:1
##
trainer:PTQ
PTQ:slim/quant/ptq.py
##
===========================infer_params===========================
inference:deploy/python/infer.py
--device:cpu|gpu
--batch_size:1
--config:quant_model/deploy.yaml
--image_path:test_tipc/cpp/cityscapes_demo.png
--benchmark:True