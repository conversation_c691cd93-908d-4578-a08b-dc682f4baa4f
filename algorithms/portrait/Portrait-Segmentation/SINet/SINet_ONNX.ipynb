{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "SINet_ONNX.ipynb", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "ibyVrOTSwF56"}, "source": ["**Install ONNX and ONNX Simplifier**"]}, {"cell_type": "code", "metadata": {"id": "mI5aZkrXe3dV"}, "source": ["!sudo apt-get install protobuf-compiler libprotoc-dev\n", "!pip install onnx\n", "!pip install onnx-simplifier\n", "!pip install torch-summary"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "PuFAltjx7CNs"}, "source": ["**Load And Convert The Model to ONNX Format**\n", "\n", "Copy the model checkpoint weights(**model_296.pth**) and model class file(**SINet.py**) into the current directory, and finally export the pytorch model to onnx format."]}, {"cell_type": "code", "metadata": {"id": "vr9RAMegvNFA"}, "source": ["import torch\n", "from SINet import *\n", "\n", "config = [[[3, 1], [5, 1]], [[3, 1], [3, 1]],\n", "              [[3, 1], [5, 1]], [[3, 1], [3, 1]], [[5, 1], [3, 2]], [[5, 2], [3, 4]],\n", "              [[3, 1], [3, 1]], [[5, 1], [5, 1]], [[3, 2], [3, 4]], [[3, 1], [5, 2]]]\n", "model = SINet(classes=2, p=2, q=8, config=config,\n", "                  chnn=1)\n", "\n", "model.load_state_dict(torch.load('/content/model_296.pth'))\n", "model.eval()\n", "\n", "dummy_input = torch.randn(1, 3, 320, 320)\n", "input_names = [ \"data\" ]\n", "output_names = [ \"classifier/1\" ]\n", "\n", "torch.onnx.export(model, dummy_input, \"SINet_320.onnx\", verbose=True, input_names=input_names, output_names=output_names, opset_version=11, export_params=True, do_constant_folding=True)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Hdxx6rhXQhDz"}, "source": ["**Note:** The original  SINet class file was modified at line 116, i.e **x.size()** to **[int(s) for s in x.size()]** . This makes the size value static and prevents error during onnx conversion. Also, some layers like **ReduceMax** may not be fully supported in **opencv onnx** runtime, so replace this operation with appropriate reshape and **maxpool** layers."]}, {"cell_type": "markdown", "metadata": {"id": "T63K94u8RvBo"}, "source": ["**Add Softmax Layer And Save Model**"]}, {"cell_type": "markdown", "metadata": {"id": "SjdwnJHaUHx9"}, "source": ["Save another version of the model with **softmax output**."]}, {"cell_type": "code", "metadata": {"id": "faYx1K2A5Kbk"}, "source": ["from torchsummary import summary\n", "\n", "soft_model = nn.Sequential(\n", "    model,\n", "    nn.Softmax(1)\n", ")\n", "\n", "# Export softmax model\n", "dummy_input = torch.randn(1, 3, 320, 320)\n", "\n", "soft_model.eval()\n", "input_names = [ \"data\" ]\n", "output_names = [ \"Softmax/1\" ]\n", "torch.onnx.export(soft_model, dummy_input, \"SINet_320_Softmax.onnx\", verbose=True, input_names=input_names, output_names=output_names, opset_version=11, export_params=True, do_constant_folding=True)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "TNknGgqyQ3iO"}, "source": ["**Optimize The Models With ONNX Simplifier**"]}, {"cell_type": "code", "metadata": {"id": "enrrHg8_QZkU"}, "source": ["!python3 -m onnxsim SINet_320_Softmax.onnx SINet_320_optim_Softmax.onnx\n", "!python3 -m onnxsim SINet_320.onnx SINet_320_optim.onnx"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "jVgVqdEOmN5t"}, "source": ["**Run Inference Using ONNX-Runtime**"]}, {"cell_type": "code", "metadata": {"id": "pigJDwhUmUfO"}, "source": ["import numpy as np\n", "import cv2\n", "import onnxruntime as rt\n", "\n", "img = cv2.imread('obama.jpg')\n", "img = cv2.resize(img, (320,320))\n", "img = img.astype(np.float32)\n", "\n", "# Preprocess images based on the original training/inference code\n", "mean = [102.890434, 111.25247,  126.91212 ]\n", "std = [62.93292,  62.82138,  66.355705]\n", "\n", "img=(img-mean)/std\n", "\n", "img /= 255\n", "img = img.transpose((2, 0, 1))\n", "img = img[np.newaxis,...]\n", "\n", "# Perform inference using the ONNX runtime\n", "sess = rt.InferenceSession(\"/content/SINet_320_optim.onnx\")\n", "input_name = sess.get_inputs()[0].name\n", "pred_onx = sess.run(None, {input_name: img.astype(np.float32)})[0]\n", "res=np.argmax(pred_onx[0], axis=0)[...,np.newaxis]"], "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "g1_US08dLyiz"}, "source": ["Perform pytorch model inference on **GPU** and **compare** the results with **ONNX** model output."]}, {"cell_type": "code", "metadata": {"id": "A1CYsThnLkUD"}, "source": ["# Enable gpu mode, if cuda available\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Load the model and inputs into GPU\n", "model.to(device)\n", "inputs=torch.from_numpy(img).float().to(device)\n", "\n", "# Perform prediction and plot results\n", "with torch.no_grad():    \n", "         torch_res = model(inputs)\n", "         _, mask = torch.max(torch_res, 1)\n", "      \n", "torch_res = torch_res.cpu().numpy()\n", "\n", "# Compare the outputs of onnx and pytorch models\n", "np.allclose(pred_onx,torch_res, rtol=1e-03, atol=1e-05)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Rtmi9OFrJhyH"}, "source": ["**Note:-** \n", "* On a dual core 2.2GHz CPU, the inference time of ONNX model is about **0.064 seconds** (i.e **15 fps**) wihtout any additional optimizations. \n", "\n", "* On **Tesla T4 GPU**, the avg. execution time of the **pytorch model** was around 0.010s(**100 fps**), whereas on CPU it was around 0.144s."]}, {"cell_type": "markdown", "metadata": {"id": "1Y8Y9Q0h_guZ"}, "source": ["**Plot The Results Using Matplotlib**"]}, {"cell_type": "code", "metadata": {"id": "Bi1eajsg8tFj"}, "source": ["import matplotlib.pyplot as plt\n", "from mpl_toolkits.axes_grid1 import ImageGrid\n", "import numpy as np\n", "\n", "# Read input and background images\n", "image = cv2.imread('obama.jpg')\n", "image = cv2.resize(image, (320,320))\n", "background =  cv2.imread('whitehouse.jpeg')\n", "background = cv2.resize(background, (320,320))\n", "\n", "# Crop image using mask and blend with background \n", "output = res*image + (1-res)*background\n", "output = cv2.cvtColor(output.astype(np.uint8), cv2.COLOR_BGR2RGB)\n", "\n", "# Plot the results using matplotlib\n", "im1 = cv2.cvtColor(image.astype(np.uint8), cv2.COLOR_BGR2RGB)\n", "im2 = cv2.cvtColor(background.astype(np.uint8), cv2.COLOR_BGR2RGB)\n", "\n", "im3 = res.squeeze()*255\n", "im4 = output\n", "\n", "fig = plt.figure(figsize=(10., 10.))\n", "grid = ImageGrid(fig, 111,  # similar to subplot(111)\n", "                 nrows_ncols=(2, 2),  # creates 2x2 grid of axes\n", "                 axes_pad=0.2,  # pad between axes in inch.\n", "                 )\n", "\n", "for ax, im in zip(grid, [im1, im2, im3, im4]):\n", "    # Iterating over the grid returns the Axes.\n", "    ax.imshow(im)\n", "\n", "plt.show()"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "75GxuhM0BYTM"}, "source": ["**Pytorch To CoreML (Experimental)**\n", "\n", "Install latest coremltools"]}, {"cell_type": "code", "metadata": {"id": "2knBTmVpb7MA"}, "source": ["!pip install --upgrade coremltools"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hIBH_aGIBi0I"}, "source": ["Load the pytorch saved model and directly convert in to **CoreML** format"]}, {"cell_type": "code", "metadata": {"id": "ra60BZxictbr"}, "source": ["from SINet import *\n", "import coremltools as ct\n", "import torch\n", "import torchvision\n", "\n", "# Load pytorch model\n", "config = [[[3, 1], [5, 1]], [[3, 1], [3, 1]],\n", "              [[3, 1], [5, 1]], [[3, 1], [3, 1]], [[5, 1], [3, 2]], [[5, 2], [3, 4]],\n", "              [[3, 1], [3, 1]], [[5, 1], [5, 1]], [[3, 2], [3, 4]], [[3, 1], [5, 2]]]\n", "model = SINet(classes=2, p=2, q=8, config=config,\n", "                  chnn=1)\n", "\n", "model.load_state_dict(torch.load('/content/model_296.pth'))\n", "model.eval()\n", "\n", "# Get a pytorch model and save it as a *.pt file\n", "pytorch_model = model\n", "pytorch_model.eval()\n", "example_input = torch.rand(1, 3, 320, 320)\n", "traced_model = torch.jit.trace(pytorch_model, example_input)\n", "traced_model.save(\"sinet.pt\")\n", "\n", "# Convert the saved PyTorch model to Core ML\n", "mlmodel = ct.convert(\"sinet.pt\",\n", "                    inputs=[ct.TensorType(shape=(1, 3, 320, 320))])\n", "\n", "# Save the coreml model\n", "mlmodel.save(\"SINet.mlmodel\")"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "CejXVr1RCJSG"}, "source": ["**Note:** The converter seems to throw an error - '**raise ValueError(\"x should be at least rank 3\")** ' due to prelu activation dimension mismatch. However if we commnet out the lines 270-278( function: type_inference(self)) in the file: /usr/local/lib/python3.6/dist-packages/coremltools/converters/mil/mil/ops/defs/activation.py., the conversion was successful and the **mlmodel** was saved. Unfortunately we cannot test the model(prediction) on a linux system; it requires macOS."]}, {"cell_type": "markdown", "metadata": {"id": "2l_z2X8FELGB"}, "source": ["**References:-**\n", "\n", "1.   https://github.com/onnx/onnx/blob/master/docs/PythonAPIOverview.md\n", "2.   https://github.com/daquexian/onnx-simplifier\n", "3. https://github.com/microsoft/onnxruntime/blob/master/docs/python/tutorial.rst#step-3-load-and-run-the-model-using-onnx-runtime\n", "4. https://github.com/clovaai/ext_portrait_segmentation\n", "5. https://coremltools.readme.io/docs/introductory-quickstart"]}]}