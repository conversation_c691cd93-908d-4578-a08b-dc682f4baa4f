batch_size: 1
iters: 80000

model:
  type: MscaleOCRNet
  pretrained: saved_model/sscale_ocr_auto_nue_map+city_ce+dice@1920/best_model/model.pdparams
  n_scales: [1.0, 1.5, 2.0]
  backbone:
    type: HRNet_W48_NV
  num_classes: 26
  backbone_indices: [0]

train_dataset:
  type: AutoNue
  dataset_root: data/IDD_Segmentation
  transforms:
    - type: Resize
      target_size: [1920, 1080]
    - type: ResizeStepScaling
      min_scale_factor: 0.5
      max_scale_factor: 2.0
      scale_step_size: 0
    - type: RandomPaddingCrop
      crop_size: [1920, 1080]
    - type: RandomHorizontalFlip
    - type: RandomDistort
      brightness_range: 0.25
      brightness_prob: 1
      contrast_range: 0.25
      contrast_prob: 1
      saturation_range: 0.25
      saturation_prob: 1
      hue_range: 63
      hue_prob: 1
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: train


val_dataset:
  type: AutoNue
  dataset_root: data/IDD_Segmentation
  transforms:
    - type: Resize
      target_size: [1920, 1080]
    - type: Normalize
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  mode: val

optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 0.0001

learning_rate:
  value: 0.005
  decay:
    type: poly
    power: 2
    end_lr: 0.0

loss:
  types:
    - type: DiceLoss
    - type: DiceLoss
    - type: BootstrappedCrossEntropyLoss
      min_K: 100000
      loss_th: 0.05
    - type: BootstrappedCrossEntropyLoss
      min_K: 100000
      loss_th: 0.05
  coef: [1, 0.4, 1, 0.4]
