English | [简体中文](losses_cn.md)
# The Detailed description of the loss function

* ## [paddleseg.models.losses.binary_cross_entropy_loss](./BCELoss_en.md)

* ## [paddleseg.models.losses.bootstrapped_cross_entropy](./BootstrappedCrossEntropyLoss_en.md)

* ## [paddleseg.models.losses.cross_entropy_loss](./CrossEntropyLoss_en.md)

* ## [paddleseg.models.losses.decoupledsegnet_relax_boundary_loss](./RelaxBoundaryLoss_en.md)

* ## [paddleseg.models.losses.dice_loss](./DiceLoss_en.md)

* ## [paddleseg.models.losses.DualTaskLoss](./DualTaskLoss_en.md)

* ## [paddleseg.models.losses.edge_attention_loss](./EdgeAttentionLoss_en.md)

* ## [paddleseg.models.losses.l1_loss](./L1Loss_en.md)

* ## [paddleseg.models.losses.Lovasz_loss](./lovasz_loss_en.md)

* ## [paddleseg.models.losses.MSELoss](./MSELoss_en.md)

* ## [paddleseg.models.losses.ohem_cross_entropy_loss](./OhemCrossEntropyLoss_en.md)

* ## [paddleseg.models.losses.ohem_edge_attention_loss](./OhemEdgeAttentionLoss_en.md)

* ## [paddleseg.models.losses.semantic_connectivity_loss](./SemanticConnectivityLoss_en.md)
