#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Portrait Pipeline: 实现人脸检测、人像分割、深度计算和背景虚化的完整流程
"""

import os
import sys
import time
import logging
import glob
import cv2
import numpy as np
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("portrait_pipeline.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("Portrait Pipeline")


# 打印环境信息
logger.info(f"Python 版本: {sys.version}")
logger.info(f"当前工作目录: {os.getcwd()}")

# 设置输入和输出路径
INPUT_DIR = "/home/<USER>/T2/data/bokeh_test"
OUTPUT_ROOT = "/home/<USER>/T2/data/pipeline_output"
FACE_DETECTION_DIR = os.path.join(OUTPUT_ROOT, "face_detection")
PORTRAIT_MASK_DIR = os.path.join(OUTPUT_ROOT, "portrait_mask")
DEPTH_DIR = os.path.join(OUTPUT_ROOT, "depth")
FACE_MASK_DIR = os.path.join(OUTPUT_ROOT, "face_mask")
BOKEH_OUTPUT_DIR = os.path.join(OUTPUT_ROOT, "bokeh_output")

# 创建输出目录
os.makedirs(OUTPUT_ROOT, exist_ok=True)
os.makedirs(FACE_DETECTION_DIR, exist_ok=True)
os.makedirs(PORTRAIT_MASK_DIR, exist_ok=True)
os.makedirs(DEPTH_DIR, exist_ok=True)
os.makedirs(FACE_MASK_DIR, exist_ok=True)
os.makedirs(BOKEH_OUTPUT_DIR, exist_ok=True)

# 设置模型路径
YOLO_FACE_MODEL = "/home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/preweight/preweight.pt"
PORTRAIT_SEG_MODEL = "/home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/inference_models/human_pp_humansegv1_server_512x512_inference_model/deploy.yaml"
DEPTH_MODEL_PATH = "/home/<USER>/code/IEDemo/algorithms/portrait/Depth-Anything-V2/checkpoints/depth_anything_v2_vitl.pth"
BOKEH_MODEL_DIR = "/home/<USER>/code/IEDemo/algorithms/portrait/BokehMe"

def face_detection(image_path, output_dir, conda_env="yolofacev2"):
    """
    使用YOLO-FaceV2进行人脸检测，返回面积最大的人脸框位置

    Args:
        image_path: 输入图像路径
        output_dir: 输出目录
        conda_env: 使用的conda环境名称

    Returns:
        face_box: 面积最大的人脸框 [x1, y1, x2, y2]
    """
    logger.info(f"执行人脸检测: {os.path.basename(image_path)}")
    logger.info(f"使用conda环境: {conda_env}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 检查权重文件是否存在
    if not os.path.exists(YOLO_FACE_MODEL):
        logger.error(f"人脸检测模型权重文件不存在: {YOLO_FACE_MODEL}")
        return None

    # 使用YOLO-FaceV2进行人脸检测
    cmd = f"conda run -n {conda_env} python3 /home/<USER>/code/IEDemo/algorithms/portrait/YOLO-FaceV2/detect.py \
            --weights {YOLO_FACE_MODEL} \
            --source {image_path} \
            --save-txt \
            --save_dir {output_dir}"

    logger.info(f"执行命令: {cmd}")
    os.system(cmd)

    # 读取检测结果
    txt_dir = os.path.join(output_dir, "labels")
    txt_file = os.path.join(txt_dir, Path(image_path).stem + ".txt")
    print(txt_file)

    face_boxes = []
    if os.path.exists(txt_file):
        with open(txt_file, 'r') as f:
            lines = f.readlines()
            for line in lines:
                parts = line.strip().split()
                # YOLO格式: class x_center y_center width height
                _, x_center, y_center, width, height = map(float, parts[:5])  # 忽略class值

                # 读取原始图像尺寸
                img = cv2.imread(image_path)
                img_height, img_width = img.shape[:2]

                # 转换为像素坐标
                x1 = int((x_center - width/2) * img_width)
                y1 = int((y_center - height/2) * img_height)
                x2 = int((x_center + width/2) * img_width)
                y2 = int((y_center + height/2) * img_height)

                # 计算面积
                area = (x2 - x1) * (y2 - y1)
                face_boxes.append(([x1, y1, x2, y2], area))

    # 如果没有检测到人脸，返回None
    if not face_boxes:
        logger.warning(f"未检测到人脸: {image_path}")
        return None

    # 返回面积最大的人脸框
    face_boxes.sort(key=lambda x: x[1], reverse=True)
    largest_face = face_boxes[0][0]

    # 在图像上绘制人脸框并保存
    output_file = os.path.join(output_dir, os.path.basename(image_path))
    img = cv2.imread(image_path)
    cv2.rectangle(img, (largest_face[0], largest_face[1]), (largest_face[2], largest_face[3]), (0, 255, 0), 2)
    cv2.putText(img, "Face", (largest_face[0], largest_face[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
    cv2.imwrite(output_file, img)

    logger.info(f"检测到最大人脸: {largest_face}")
    return largest_face

def portrait_segmentation(image_path, output_dir, conda_env="paddleseg"):
    """
    使用PaddleSeg/PP-HumanSeg进行人像分割

    Args:
        image_path: 输入图像路径
        output_dir: 输出目录
        conda_env: 使用的conda环境名称

    Returns:
        mask_path: 人像mask路径
    """
    logger.info(f"执行人像分割: {os.path.basename(image_path)}")
    logger.info(f"使用conda环境: {conda_env}")

    # 输出文件路径
    output_file = os.path.join(output_dir, os.path.basename(image_path))
    mask_file = os.path.join(output_dir, "mask_" + os.path.basename(image_path))

    # 使用PP-HumanSeg进行人像分割
    cmd = f"conda run -n {conda_env} python3 /home/<USER>/code/IEDemo/algorithms/portrait/PaddleSeg/contrib/PP-HumanSeg/src/seg_demo.py \
            --config {PORTRAIT_SEG_MODEL} \
            --img_path {image_path} \
            --save_dir {output_file} \
            --save_maskdir {mask_file}"

    logger.info(f"执行命令: {cmd}")
    os.system(cmd)

    logger.info(f"人像分割完成，mask保存至: {mask_file}")
    return mask_file

def depth_estimation(image_path, output_dir, conda_env="depthanything2"):
    """
    使用Depth-Anything-V2计算深度图

    Args:
        image_path: 输入图像路径
        output_dir: 输出目录
        conda_env: 使用的conda环境名称

    Returns:
        depth_path: 深度图路径
    """
    logger.info(f"执行深度估计: {os.path.basename(image_path)}")
    logger.info(f"使用conda环境: {conda_env}")

    # 使用Depth-Anything-V2计算深度图
    cmd = f"conda run -n {conda_env} python3 /home/<USER>/code/IEDemo/algorithms/portrait/Depth-Anything-V2/run_depth.py \
            --encoder vitl \
            --img-path {image_path} \
            --outdir {output_dir}"

    logger.info(f"执行命令: {cmd}")
    os.system(cmd)

    # 深度图输出路径
    depth_file = os.path.join(output_dir, Path(image_path).stem + ".pfm")

    if not os.path.exists(depth_file):
        logger.error(f"深度估计失败，未生成深度图: {depth_file}")
        return None

    logger.info(f"深度估计完成，深度图保存至: {depth_file}")
    return depth_file

def calculate_face_depth(image_path, face_box, mask_path, depth_path, output_dir):
    """
    计算人像区域的平均深度，优先使用人脸区域，如果没有人脸则使用整个人像区域

    Args:
        image_path: 原始图像路径
        face_box: 人脸框 [x1, y1, x2, y2]，如果检测到人脸则不为None
        mask_path: 人像mask路径
        depth_path: 深度图路径
        output_dir: 输出目录

    Returns:
        face_depth: 人脸区域或人像区域的平均深度值
    """
    logger.info(f"计算深度: {os.path.basename(image_path)}")

    # 如果深度图不存在，返回默认深度值0.5
    if depth_path is None or not os.path.exists(depth_path):
        logger.warning(f"深度图不存在，使用默认深度值0.5")
        return 0.5

    # 读取原始图像、人像mask和深度图
    img = cv2.imread(image_path)
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    depth = cv2.imread(depth_path, cv2.IMREAD_GRAYSCALE)

    # 确保mask和depth与原始图像尺寸一致
    if mask.shape[:2] != img.shape[:2]:
        mask = cv2.resize(mask, (img.shape[1], img.shape[0]))
    if depth.shape[:2] != img.shape[:2]:
        depth = cv2.resize(depth, (img.shape[1], img.shape[0]))

    # 保存人像mask的副本
    portrait_mask_path = os.path.join(output_dir, "portrait_mask_" + os.path.basename(image_path))
    cv2.imwrite(portrait_mask_path, mask)

    # 归一化深度图
    depth_normalized = depth.astype(np.float32) / 255.0
    
    # 创建可视化图像
    depth_vis = cv2.applyColorMap(depth, cv2.COLORMAP_INFERNO)
    depth_vis_path = os.path.join(output_dir, "depth_vis_" + os.path.basename(image_path))
    cv2.imwrite(depth_vis_path, depth_vis)
    
    # 可视化人像mask（叠加在原图上）
    mask_vis = img.copy()
    mask_vis[mask > 0] = [0, 255, 0]  # 绿色标记人像区域
    mask_vis_path = os.path.join(output_dir, "portrait_mask_vis_" + os.path.basename(image_path))
    cv2.imwrite(mask_vis_path, mask_vis)

    # 如果检测到人脸，优先计算人脸区域的平均深度
    if face_box is not None:
        x1, y1, x2, y2 = face_box
        
        # 扩大人脸框以包含更多面部区域（扩大20%）
        face_width = x2 - x1
        face_height = y2 - y1
        x1_expanded = max(0, int(x1 - face_width * 0.1))
        y1_expanded = max(0, int(y1 - face_height * 0.1))
        x2_expanded = min(img.shape[1], int(x2 + face_width * 0.1))
        y2_expanded = min(img.shape[0], int(y2 + face_height * 0.1))
        
        # 创建人脸区域mask
        face_mask = np.zeros_like(mask)
        face_mask[y1_expanded:y2_expanded, x1_expanded:x2_expanded] = 255
        
        # 保存人脸mask
        face_mask_path = os.path.join(output_dir, "face_mask_" + os.path.basename(image_path))
        cv2.imwrite(face_mask_path, face_mask)
        
        # 可视化人脸区域（叠加在原图上）
        face_vis = img.copy()
        face_vis[face_mask > 0] = [0, 0, 255]  # 红色标记人脸区域
        face_vis_path = os.path.join(output_dir, "face_region_vis_" + os.path.basename(image_path))
        cv2.imwrite(face_vis_path, face_vis)
        
        # 计算人脸区域的平均深度
        face_depth_values = depth_normalized[face_mask > 0]
        
        if len(face_depth_values) > 0:
            face_depth = np.mean(face_depth_values)
            logger.info(f"使用人脸区域平均深度: {face_depth}")
            
            # 创建组合可视化图像（原图+人脸区域+深度图）
            combined_vis = np.hstack([img, face_vis, depth_vis])
            combined_vis_path = os.path.join(output_dir, "combined_vis_" + os.path.basename(image_path))
            cv2.imwrite(combined_vis_path, combined_vis)
            
            return face_depth
    
    # 如果没有人脸或人脸区域深度计算失败，使用整个人像区域
    portrait_depth_values = depth_normalized[mask > 0]
    
    if len(portrait_depth_values) > 0:
        portrait_depth = np.mean(portrait_depth_values)
        logger.info(f"使用整个人像区域平均深度: {portrait_depth}")
        return portrait_depth
    else:
        logger.warning(f"无法计算深度，使用默认深度值0.5")
        return 0.5

def bokeh_effect(image_path, depth_path, face_depth, output_dir, conda_env="bokehme"):
    """
    使用BokehMe进行背景虚化

    Args:
        image_path: 输入图像路径
        depth_path: 深度图路径
        face_depth: 人脸区域的平均深度值
        output_dir: 输出目录
        conda_env: 使用的conda环境名称

    Returns:
        dict: 包含处理结果的字典
            - success: 是否成功
            - output_path: 虚化效果图路径
            - message: 处理信息
    """
    logger.info(f"执行背景虚化: {os.path.basename(image_path)}")
    logger.info(f"使用人脸深度值: {face_depth}")
    logger.info(f"使用conda环境: {conda_env}")

    # 输出文件路径
    final_output_path = os.path.join(output_dir, os.path.basename(image_path))

    # 使用BokehMe进行背景虚化
    cmd = f"conda run -n {conda_env} python3 /home/<USER>/code/IEDemo/algorithms/portrait/BokehMe/demo.py \
            --image_path {image_path} \
            --disp_path {depth_path} \
            --save_dir {output_dir} \
            --K 10 \
            --disp_focus {face_depth} \
            --gamma 4 \
            "

    logger.info(f"执行命令: {cmd}")
    ret_code = os.system(cmd)

    if ret_code != 0:
        logger.error(f"BokehMe执行失败，返回码: {ret_code}")
        return {
            "success": False,
            "output_path": None,
            "message": f"BokehMe执行失败，返回码: {ret_code}"
        }

    # BokehMe输出路径在子目录中
    bokeh_dir = os.path.join(output_dir, Path(image_path).stem)
    bokeh_pred_path = os.path.join(bokeh_dir, "bokeh_pred.jpg")

    if os.path.exists(bokeh_pred_path):
        # 复制到输出目录
        try:
            bokeh_img = cv2.imread(bokeh_pred_path)
            cv2.imwrite(final_output_path, bokeh_img)
            logger.info(f"背景虚化完成，结果保存至: {final_output_path}")
            return {
                "success": True,
                "output_path": final_output_path,
                "message": "背景虚化处理成功"
            }
        except Exception as e:
            logger.error(f"保存虚化结果时出错: {str(e)}")
            return {
                "success": False,
                "output_path": bokeh_pred_path,
                "message": f"保存虚化结果时出错: {str(e)}"
            }
    else:
        logger.warning(f"未找到虚化结果: {bokeh_pred_path}")
        return {
            "success": False,
            "output_path": None,
            "message": f"未找到虚化结果: {bokeh_pred_path}"
        }

def main(single_image=False, face_env="yolofacev2", mask_env="paddleseg", depth_env="depthanything2", bokeh_env="bokehme"):
    """
    主函数，执行完整的处理流程

    Args:
        single_image: 是否只处理第一张图像（用于测试）
        face_env: 人脸检测使用的conda环境
        mask_env: 人像分割使用的conda环境
        depth_env: 深度估计使用的conda环境
        bokeh_env: 背景虚化使用的conda环境
    """
    start_time = time.time()
    logger.info("="*50)
    logger.info("开始执行Portrait Pipeline")
    logger.info("="*50)

    # 确保输出目录存在
    logger.info(f"输入目录: {INPUT_DIR}")
    logger.info(f"输出根目录: {OUTPUT_ROOT}")
    logger.info(f"使用的conda环境:")
    logger.info(f"  - 人脸检测: {face_env}")
    logger.info(f"  - 人像分割: {mask_env}")
    logger.info(f"  - 深度估计: {depth_env}")
    logger.info(f"  - 背景虚化: {bokeh_env}")

    # 获取输入目录中的所有图像
    image_files = glob.glob(os.path.join(INPUT_DIR, "*.jpg"))

    if not image_files:
        logger.error(f"未在 {INPUT_DIR} 目录下找到任何jpg图像")
        return

    logger.info(f"找到 {len(image_files)} 张图像")

    # 如果只处理一张图像，则只取第一张
    if single_image:
        logger.info("单图像模式：只处理第一张图像")
        image_files = image_files[:1]

    # 处理每张图像
    for i, image_path in enumerate(image_files):
        img_start_time = time.time()
        logger.info(f"[{i+1}/{len(image_files)}] 开始处理图像: {os.path.basename(image_path)}")

        try:
            # 1. 人脸检测
            logger.info("步骤1: 人脸检测")
            face_box = face_detection(image_path, FACE_DETECTION_DIR, conda_env=face_env)

            # 2. 人像分割
            logger.info("步骤2: 人像分割")
            mask_path = portrait_segmentation(image_path, PORTRAIT_MASK_DIR, conda_env=mask_env)

            # 3. 深度计算
            logger.info("步骤3: 深度估计")
            depth_path = depth_estimation(image_path, DEPTH_DIR, conda_env=depth_env)

            # 4. 计算人像区域平均深度
            logger.info("步骤4: 计算人像区域平均深度")
            face_depth = calculate_face_depth(image_path, face_box, mask_path, depth_path, FACE_MASK_DIR)

            # 5. 背景虚化
            logger.info("步骤5: 背景虚化")
            bokeh_result = bokeh_effect(image_path, depth_path, face_depth, BOKEH_OUTPUT_DIR, conda_env=bokeh_env)

            # 记录处理结果
            img_end_time = time.time()
            processing_time = img_end_time - img_start_time

            if bokeh_result["success"]:
                logger.info(f"图像处理成功: {os.path.basename(image_path)}")
                logger.info(f"输出文件: {bokeh_result['output_path']}")
                logger.info(f"处理耗时: {processing_time:.2f}秒")
            else:
                logger.warning(f"图像处理部分失败: {os.path.basename(image_path)}")
                logger.warning(f"失败原因: {bokeh_result['message']}")
                logger.info(f"处理耗时: {processing_time:.2f}秒")

        except Exception as e:
            logger.error(f"处理图像 {os.path.basename(image_path)} 时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            continue

    end_time = time.time()
    total_time = end_time - start_time
    logger.info("="*50)
    logger.info(f"Portrait Pipeline 执行完毕, 总耗时: {total_time:.2f}秒")
    logger.info("="*50)

def parse_args():
    """解析命令行参数"""
    import argparse
    parser = argparse.ArgumentParser(description="Portrait Pipeline: 人脸检测、人像分割、深度计算和背景虚化的完整流程")

    parser.add_argument("--input_dir", type=str, default=INPUT_DIR,
                        help=f"输入图像目录，默认: {INPUT_DIR}")
    parser.add_argument("--output_dir", type=str, default=OUTPUT_ROOT,
                        help=f"输出根目录，默认: {OUTPUT_ROOT}")
    parser.add_argument("--single_image", action="store_true",
                        help="只处理第一张图像（用于测试）")
    parser.add_argument("--face_env", type=str, default="yolofacev2",
                        help="人脸检测使用的conda环境，默认: yolofacev2")
    parser.add_argument("--mask_env", type=str, default="paddleseg",
                        help="人像分割使用的conda环境，默认: paddleseg")
    parser.add_argument("--depth_env", type=str, default="depthanything2",
                        help="深度估计使用的conda环境，默认: depthanything2")
    parser.add_argument("--bokeh_env", type=str, default="bokehme",
                        help="背景虚化使用的conda环境，默认: bokehme")

    return parser.parse_args()

def update_paths(input_dir, output_dir):
    """更新全局路径变量"""
    global INPUT_DIR, OUTPUT_ROOT
    global FACE_DETECTION_DIR, PORTRAIT_MASK_DIR, DEPTH_DIR, FACE_MASK_DIR, BOKEH_OUTPUT_DIR

    # 更新输入和输出目录
    INPUT_DIR = input_dir
    OUTPUT_ROOT = output_dir

    # 更新子目录
    FACE_DETECTION_DIR = os.path.join(OUTPUT_ROOT, "face_detection")
    PORTRAIT_MASK_DIR = os.path.join(OUTPUT_ROOT, "portrait_mask")
    DEPTH_DIR = os.path.join(OUTPUT_ROOT, "depth")
    FACE_MASK_DIR = os.path.join(OUTPUT_ROOT, "face_mask")
    BOKEH_OUTPUT_DIR = os.path.join(OUTPUT_ROOT, "bokeh_output")

    # 创建输出目录
    os.makedirs(OUTPUT_ROOT, exist_ok=True)
    os.makedirs(FACE_DETECTION_DIR, exist_ok=True)
    os.makedirs(PORTRAIT_MASK_DIR, exist_ok=True)
    os.makedirs(DEPTH_DIR, exist_ok=True)
    os.makedirs(FACE_MASK_DIR, exist_ok=True)
    os.makedirs(BOKEH_OUTPUT_DIR, exist_ok=True)

if __name__ == "__main__":
    args = parse_args()
    update_paths(args.input_dir, args.output_dir)
    main(
        single_image=args.single_image,
        face_env=args.face_env,
        mask_env=args.mask_env,
        depth_env=args.depth_env,
        bokeh_env=args.bokeh_env
    )
