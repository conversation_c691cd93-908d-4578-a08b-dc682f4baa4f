
# conda env 
conda create -n paddleseg --clone base -y

# reset conda source 
conda config --remove-key channels
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
conda config --set show_channel_urls yes
# 查看效果
cat ~/.condarc


# setup paddle
conda install paddlepaddle-gpu==3.0.0b1 paddlepaddle-cuda=11.8 -c paddle -c nvidia
python3 -c "import paddle; paddle.utils.run_check()"

# setup paddleseg
git checkout -b release/2.6 origin/release/2.6
pip install -r requirements.txt
pip install -v -e .

# infernece 
cd contrib/PP-HumanSeg
python3 src/download_inference_models.py
python3 src/seg_demo.py \
  --config inference_models/portrait_pp_humansegv2_lite_256x144_inference_model_with_softmax/deploy.yaml \
  --img_path data/portrait_shu.jpg \
  --save_dir output/human_seg_result.jpg   


python3 src/seg_demo.py \
  --config inference_models/human_pp_humansegv1_server_512x512_inference_model_with_softmax/deploy.yaml \
  --img_path data/portrait_shu.jpg \
  --save_dir output/human_seg_result.jpg 


python3 src/seg_demo.py \
  --config inference_models/human_pp_humansegv1_server_512x512_inference_model/deploy.yaml \
  --img_path data/portrait_shu.jpg \
  --save_dir output/human_seg_result.jpg 
