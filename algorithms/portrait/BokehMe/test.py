import os
import sys

# set path  of BokehMe directory
bokeh_test_folder = "/home/<USER>/data/bokeh_test"
depth_test_folder = "/home/<USER>/data/bokeh_test_depth"
bokeh_test_output_folder = "/home/<USER>/data/bokeh_test_output"


# list all tif files in the current directory
files = os.listdir(bokeh_test_folder, )

# loop through all files and print their names
for file in files:
    print(file)
    input_file = os.path.join(bokeh_test_folder, file)
    disp_file  = input_file.replace('.jpg', '.png')
    disp_file  = disp_file.replace('bokeh_test', 'bokeh_test_depth')
    output_file = os.path.join(bokeh_test_output_folder, file)
    print("input_file:", input_file)
    print("disp_file:", disp_file)
    print("output_file:", output_file)
    cmd = f"python3 demo.py --image_path {input_file} --disp_path {disp_file} --save_dir {bokeh_test_output_folder} --K 30 --disp_focus 0.3 --gamma 4 --highlight"
    os.system(cmd)