batch_size: 4
iters: 1000

train_dataset:
  type: OpticDiscSeg
  dataset_root: data/optic_disc_seg
  transforms:
    - type: Resize
      target_size: [512, 512]
    - type: RandomHorizontalFlip
    - type: Normalize
  mode: train

val_dataset:
  type: OpticDiscSeg
  dataset_root: data/optic_disc_seg
  transforms:
    - type: Normalize
  mode: val

optimizer:
  type: sgd
  momentum: 0.9
  weight_decay: 4.0e-5

lr_scheduler:
  type: PolynomialDecay
  learning_rate: 0.01
  end_lr: 0
  power: 0.9

loss:
  types:
    - type: CrossEntropyLoss
  coef: [1, 1, 1, 1, 1]

model:
  type: BiSeNetV2
  pretrained: Null
