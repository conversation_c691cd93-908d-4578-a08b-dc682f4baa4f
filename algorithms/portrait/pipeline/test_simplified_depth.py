#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for simplified depth processing logic
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_raw import Simplified<PERSON>ortrait<PERSON><PERSON><PERSON>e

def test_simplified_depth_processing():
    """Test the simplified depth processing with two clear modes"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/p15.jpg"
    output_dir = "/home/<USER>/T2/data/simplified_depth_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        print("Please update the test_image path in the script")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*60)
    print("Testing Simplified Depth Processing")
    print("="*60)
    
    # Test the two simplified modes
    test_configs = [
        {
            'name': 'normalized_depth',
            'config': {
                'normalize_depth': True,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            },
            'description': 'Min-max normalization to 0-1 range'
        },
        {
            'name': 'original_depth',
            'config': {
                'normalize_depth': False,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            },
            'description': 'Keep original depth range without normalization'
        }
    ]
    
    for test_config in test_configs:
        print(f"\nTesting: {test_config['name']}")
        print(f"Description: {test_config['description']}")
        print("-" * 50)
        
        try:
            # Initialize pipeline with specific config
            pipeline = SimplifiedPortraitPipeline(bokeh_config=test_config['config'])
            
            # Process image
            output_path = os.path.join(output_dir, f"bokeh_{test_config['name']}.jpg")
            intermediate_dir = os.path.join(output_dir, f"intermediates_{test_config['name']}")
            
            result = pipeline.process_image(
                test_image,
                output_path,
                save_intermediates=True,
                intermediate_dir=intermediate_dir
            )
            
            print(f"✓ SUCCESS: {output_path}")
            print(f"✓ Time: {result['processing_time']:.2f}s")
            print(f"✓ Focus depth: {result['focus_depth']:.3f}")
            
            if result['face_box'] is not None:
                print(f"✓ Face detected: {result['face_box']}")
            else:
                print("✓ No face detected (using portrait/center region)")
            
            print(f"✓ Raw depth range: [{result['raw_depth_map'].min():.3f}, {result['raw_depth_map'].max():.3f}]")
            print(f"✓ Processed depth range: [{result['processed_depth_map'].min():.3f}, {result['processed_depth_map'].max():.3f}]")
            
            # Verify the processing logic
            if test_config['config']['normalize_depth']:
                expected_min, expected_max = 0.0, 1.0
                actual_min, actual_max = result['processed_depth_map'].min(), result['processed_depth_map'].max()
                if abs(actual_min - expected_min) < 1e-6 and abs(actual_max - expected_max) < 1e-6:
                    print("✓ Normalization verified: depth is in [0, 1] range")
                else:
                    print(f"⚠ Normalization issue: expected [0, 1], got [{actual_min:.6f}, {actual_max:.6f}]")
            else:
                raw_min, raw_max = result['raw_depth_map'].min(), result['raw_depth_map'].max()
                proc_min, proc_max = result['processed_depth_map'].min(), result['processed_depth_map'].max()
                if abs(raw_min - proc_min) < 1e-6 and abs(raw_max - proc_max) < 1e-6:
                    print("✓ Original range preserved: processed depth matches raw depth")
                else:
                    print(f"⚠ Range preservation issue: raw [{raw_min:.3f}, {raw_max:.3f}] != processed [{proc_min:.3f}, {proc_max:.3f}]")
            
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")
            import traceback
            traceback.print_exc()

def test_command_line_simplified():
    """Test the simplified command line interface"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/p15.jpg"
    output_dir = "/home/<USER>/T2/data/cmdline_simplified_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("\n" + "="*60)
    print("Testing Simplified Command Line Interface")
    print("="*60)
    
    import subprocess
    import sys
    
    # Test simplified command line options
    cmd_tests = [
        {
            'name': 'normalized_depth_default',
            'cmd': [
                sys.executable, 'pipeline_raw.py',
                '--input', test_image,
                '--output', os.path.join(output_dir, 'normalized.jpg')
            ],
            'description': 'Default normalized depth (should normalize to 0-1)'
        },
        {
            'name': 'original_depth_preserved',
            'cmd': [
                sys.executable, 'pipeline_raw.py',
                '--input', test_image,
                '--output', os.path.join(output_dir, 'original.jpg'),
                '--no-normalize-depth'
            ],
            'description': 'Preserve original depth range'
        },
        {
            'name': 'custom_bokeh_params',
            'cmd': [
                sys.executable, 'pipeline_raw.py',
                '--input', test_image,
                '--output', os.path.join(output_dir, 'custom.jpg'),
                '--blur-strength', '40',
                '--gamma', '3.0',
                '--defocus-scale', '15'
            ],
            'description': 'Custom bokeh parameters with normalized depth'
        }
    ]
    
    for test in cmd_tests:
        print(f"\nTesting: {test['name']}")
        print(f"Description: {test['description']}")
        print(f"Command: {' '.join(test['cmd'])}")
        print("-" * 40)
        
        try:
            # Change to the correct directory
            original_cwd = os.getcwd()
            script_dir = os.path.dirname(os.path.abspath(__file__))
            os.chdir(script_dir)
            
            result = subprocess.run(test['cmd'], capture_output=True, text=True, timeout=300)
            
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                print("✓ SUCCESS: Command completed successfully")
                # Extract key information from output
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-10:]:  # Last 10 lines
                    if 'depth' in line.lower() or 'focus' in line.lower() or 'time' in line.lower():
                        print(f"  {line}")
            else:
                print(f"❌ FAILED: Return code {result.returncode}")
                if result.stderr:
                    print("Error:", result.stderr.strip()[-200:])  # Last 200 chars
                    
        except subprocess.TimeoutExpired:
            print("❌ FAILED: Command timed out")
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")

def test_depth_processing_logic():
    """Test the depth processing logic directly"""
    
    print("\n" + "="*60)
    print("Testing Depth Processing Logic")
    print("="*60)
    
    import numpy as np
    
    # Create test depth maps with different characteristics
    test_cases = [
        {
            'name': 'small_range',
            'depth': np.random.uniform(0, 1, (100, 100)).astype(np.float32),
            'description': 'Depth values already in [0, 1] range'
        },
        {
            'name': 'large_range',
            'depth': np.random.uniform(0, 500, (100, 100)).astype(np.float32),
            'description': 'Depth values in [0, 500] range'
        },
        {
            'name': 'negative_range',
            'depth': np.random.uniform(-100, 100, (100, 100)).astype(np.float32),
            'description': 'Depth values in [-100, 100] range'
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print("-" * 40)
        
        depth_map = test_case['depth']
        print(f"Input depth range: [{depth_map.min():.3f}, {depth_map.max():.3f}]")
        
        # Test normalized mode
        pipeline_norm = SimplifiedPortraitPipeline(bokeh_config={'normalize_depth': True})
        processed_norm = pipeline_norm.process_depth_map(depth_map)
        print(f"Normalized result: [{processed_norm.min():.6f}, {processed_norm.max():.6f}]")
        
        # Test original mode
        pipeline_orig = SimplifiedPortraitPipeline(bokeh_config={'normalize_depth': False})
        processed_orig = pipeline_orig.process_depth_map(depth_map)
        print(f"Original result: [{processed_orig.min():.3f}, {processed_orig.max():.3f}]")
        
        # Verify results
        if abs(processed_norm.min() - 0.0) < 1e-6 and abs(processed_norm.max() - 1.0) < 1e-6:
            print("✓ Normalization correct")
        else:
            print("❌ Normalization failed")
            
        if np.allclose(processed_orig, depth_map):
            print("✓ Original preservation correct")
        else:
            print("❌ Original preservation failed")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Simplified Depth Processing')
    parser.add_argument('--test', choices=['depth', 'cmdline', 'logic', 'all'], 
                       default='all', help='Which test to run')
    
    args = parser.parse_args()
    
    if args.test in ['depth', 'all']:
        test_simplified_depth_processing()
    
    if args.test in ['cmdline', 'all']:
        test_command_line_simplified()
    
    if args.test in ['logic', 'all']:
        test_depth_processing_logic()
    
    print("\n" + "="*60)
    print("All tests completed!")
    print("="*60)
