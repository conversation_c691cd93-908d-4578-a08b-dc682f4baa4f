#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the improved BokehMe pipeline implementation
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_raw import SimplifiedPortraitPipeline

def test_improved_pipeline():
    """Test the improved pipeline with the problematic image"""
    
    test_image = "/home/<USER>/T2/data/bokeh_test/p15.jpg"
    output_dir = "/home/<USER>/T2/data/improved_pipeline_test"
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        print("Please update the test_image path in the script")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*60)
    print("Testing Improved BokehMe Pipeline")
    print("="*60)
    
    # Test different configurations
    test_configs = [
        {
            'name': 'full_pipeline_improved',
            'config': {
                'use_classical_only': False,
                'normalize_depth': True,
                'depth_min_percentile': 5,
                'depth_max_percentile': 95,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            },
            'description': 'Full neural + classical pipeline with improved multi-scale processing'
        },
        {
            'name': 'classical_only_safe',
            'config': {
                'use_classical_only': True,
                'K': 60.0,
                'gamma': 4.0,
                'defocus_scale': 10.0
            },
            'description': 'Classical rendering only (safe fallback)'
        },
        {
            'name': 'conservative_neural',
            'config': {
                'use_classical_only': False,
                'normalize_depth': True,
                'depth_min_percentile': 10,
                'depth_max_percentile': 90,
                'K': 40.0,
                'gamma': 3.0,
                'defocus_scale': 15.0
            },
            'description': 'Conservative neural network settings'
        },
        {
            'name': 'preserve_depth_neural',
            'config': {
                'use_classical_only': False,
                'preserve_depth_range': True,
                'normalize_depth': False,
                'K': 50.0,
                'gamma': 3.5,
                'defocus_scale': 12.0
            },
            'description': 'Preserve depth range with neural processing'
        }
    ]
    
    for test_config in test_configs:
        print(f"\nTesting: {test_config['name']}")
        print(f"Description: {test_config['description']}")
        print("-" * 50)
        
        try:
            # Initialize pipeline with specific config
            pipeline = SimplifiedPortraitPipeline(bokeh_config=test_config['config'])
            
            # Process image
            output_path = os.path.join(output_dir, f"bokeh_{test_config['name']}.jpg")
            intermediate_dir = os.path.join(output_dir, f"intermediates_{test_config['name']}")
            
            result = pipeline.process_image(
                test_image,
                output_path,
                save_intermediates=True,
                intermediate_dir=intermediate_dir
            )
            
            print(f"✓ SUCCESS: {output_path}")
            print(f"✓ Time: {result['processing_time']:.2f}s")
            print(f"✓ Focus depth: {result['focus_depth']:.3f}")
            
            if result['face_box'] is not None:
                print(f"✓ Face detected: {result['face_box']}")
            else:
                print("✓ No face detected (using portrait/center region)")
            
            print(f"✓ Raw depth range: [{result['raw_depth_map'].min():.3f}, {result['raw_depth_map'].max():.3f}]")
            print(f"✓ Processed depth range: [{result['processed_depth_map'].min():.3f}, {result['processed_depth_map'].max():.3f}]")
            
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")
            import traceback
            traceback.print_exc()

def test_multi_scale_processing():
    """Test the multi-scale processing with different adapt_scale values"""
    
    import numpy as np
    import cv2
    import torch
    
    output_dir = "/home/<USER>/T2/data/multi_scale_test"
    os.makedirs(output_dir, exist_ok=True)
    
    print("\n" + "="*60)
    print("Testing Multi-Scale Processing")
    print("="*60)
    
    # Create synthetic test cases with different defocus ranges
    scale_tests = [
        {
            'name': 'small_defocus',
            'defocus_range': (-2, 2),
            'expected_adapt_scale': 2,
            'description': 'Small defocus values (adapt_scale ~2)'
        },
        {
            'name': 'medium_defocus',
            'defocus_range': (-4, 4),
            'expected_adapt_scale': 4,
            'description': 'Medium defocus values (adapt_scale ~4)'
        },
        {
            'name': 'large_defocus',
            'defocus_range': (-8, 8),
            'expected_adapt_scale': 8,
            'description': 'Large defocus values (adapt_scale ~8)'
        }
    ]
    
    for test in scale_tests:
        print(f"\nTesting: {test['name']}")
        print(f"Description: {test['description']}")
        print("-" * 40)
        
        try:
            # Create synthetic image and defocus map
            h, w = 400, 600
            test_image = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
            
            # Add some structure
            center_x, center_y = w // 2, h // 2
            cv2.circle(test_image, (center_x, center_y), min(w, h) // 6, (255, 255, 255), -1)
            cv2.circle(test_image, (center_x, center_y), min(w, h) // 8, (128, 128, 128), -1)
            
            test_image_path = os.path.join(output_dir, f"test_{test['name']}.jpg")
            cv2.imwrite(test_image_path, test_image)
            
            # Create synthetic defocus map with specific range
            defocus_min, defocus_max = test['defocus_range']
            synthetic_defocus = np.random.uniform(defocus_min, defocus_max, (h, w)).astype(np.float32)
            
            # Add radial gradient for more realistic defocus
            y, x = np.ogrid[:h, :w]
            center_dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            center_dist = center_dist / center_dist.max()
            synthetic_defocus = synthetic_defocus * center_dist
            
            # Test with classical only for safety
            pipeline = SimplifiedPortraitPipeline(bokeh_config={'use_classical_only': True})
            
            # Manually test the defocus calculation
            defocus_tensor = torch.from_numpy(synthetic_defocus).unsqueeze(0).unsqueeze(0).to(pipeline.device)
            adapt_scale = max(defocus_tensor.abs().max().item(), 1)
            
            print(f"✓ Defocus range: [{synthetic_defocus.min():.3f}, {synthetic_defocus.max():.3f}]")
            print(f"✓ Calculated adapt_scale: {adapt_scale:.2f}")
            print(f"✓ Expected scales to process: {int(np.log2(adapt_scale))}")
            
            # Process the image
            output_path = os.path.join(output_dir, f"result_{test['name']}.jpg")
            
            result = pipeline.process_image(
                test_image_path,
                output_path,
                save_intermediates=False
            )
            
            print(f"✓ Processing successful: {output_path}")
            print(f"✓ Time: {result['processing_time']:.2f}s")
            
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")

def test_gaussian_blur_function():
    """Test the Gaussian blur function implementation"""
    
    print("\n" + "="*60)
    print("Testing Gaussian Blur Function")
    print("="*60)
    
    import torch
    
    try:
        # Initialize pipeline to access the gaussian blur function
        pipeline = SimplifiedPortraitPipeline()
        
        # Test different blur radii
        blur_tests = [
            {'r': 1, 'size': (100, 100)},
            {'r': 3, 'size': (200, 200)},
            {'r': 5, 'size': (300, 300)},
        ]
        
        for test in blur_tests:
            r = test['r']
            h, w = test['size']
            
            # Create test tensor
            test_tensor = torch.ones(1, 1, h, w, device=pipeline.device)
            
            # Apply Gaussian blur
            blurred = pipeline._gaussian_blur(test_tensor, r)
            
            print(f"✓ Blur radius {r} on {h}x{w}: input shape {test_tensor.shape} -> output shape {blurred.shape}")
            print(f"  Input sum: {test_tensor.sum().item():.1f}, Output sum: {blurred.sum().item():.1f}")
            
            # Check that output has same size as input
            assert blurred.shape == test_tensor.shape, f"Shape mismatch: {blurred.shape} != {test_tensor.shape}"
            
        print("✓ All Gaussian blur tests passed")
        
    except Exception as e:
        print(f"❌ Gaussian blur test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Improved BokehMe Pipeline')
    parser.add_argument('--test', choices=['pipeline', 'multiscale', 'gaussian', 'all'], 
                       default='all', help='Which test to run')
    
    args = parser.parse_args()
    
    if args.test in ['pipeline', 'all']:
        test_improved_pipeline()
    
    if args.test in ['multiscale', 'all']:
        test_multi_scale_processing()
    
    if args.test in ['gaussian', 'all']:
        test_gaussian_blur_function()
    
    print("\n" + "="*60)
    print("All tests completed!")
    print("="*60)
