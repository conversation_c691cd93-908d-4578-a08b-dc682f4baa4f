_base_: './base_cfg.yml'

model:
  type: DeepLabV3P
  backbone:
    type: ResNet50_vd
    output_stride: 8
    multi_grid: [1, 2, 4]
    pretrained: https://bj.bcebos.com/paddleseg/dygraph/resnet50_vd_ssld_v2.tar.gz
  backbone_indices: [0, 3]
  aspp_ratios: [1, 12, 24, 36]
  aspp_out_channels: 256
  align_corners: False
  pretrained: null


loss:
  types:
    - type: MixedLoss
      losses:
        - type: OhemCrossEntropyLoss
          min_kept: 65000
        - type: LovaszSoftmaxLoss
      coef: [0.8, 0.2]
  coef: [1]