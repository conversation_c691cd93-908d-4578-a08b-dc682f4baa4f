{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "slimnet_mediapipe.ipynb", "provenance": [{"file_id": "1cK5AItB7bAlJtQ6VO311AixfcxR3ltfF", "timestamp": 1600450458089}], "collapsed_sections": [], "mount_file_id": "1cK5AItB7bAlJtQ6VO311AixfcxR3ltfF", "authorship_tag": "ABX9TyP5GW4An1SCpwmt382MkhCj"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "YUd_t1PJZ4F7", "colab_type": "text"}, "source": ["**Portrait Segmentation With Slim-Net and Mediapipe**\n", "\n", "Mediapipe is an **open-source** framework for developing machine learning applications in **mobile**, desktop, web and IoT devices. Portrait segmentation refers to the process of segmenting a **person** in an image from its background.Here, we will develop an **android application** for portrait segmentaion using mediapipe and **tflite**."]}, {"cell_type": "markdown", "metadata": {"id": "cvqVY_ONWGpG", "colab_type": "text"}, "source": ["**1. Checkout MediaPipe Github Repository**\n", "\n", "The mediapipe **repository** contains many demo applications for android. We will modify the hair_segmentaion application, which contains the basic pipeline for **video segmentaion**.\n", "\n"]}, {"cell_type": "code", "metadata": {"id": "m86Lz_m7pTK_", "colab_type": "code", "colab": {}}, "source": ["!git clone https://github.com/google/mediapipe.git"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ytXfsYn9V7VL", "colab_type": "text"}, "source": ["**2. <PERSON><PERSON><PERSON> <PERSON><PERSON> on Ubuntu**\n", "\n", "Setup **baze**l on your system to build and deploy the **android** application."]}, {"cell_type": "code", "metadata": {"id": "EdGchah0pgIf", "colab_type": "code", "colab": {}}, "source": ["!sudo apt install curl gnupg\n", "!curl -fsSL https://bazel.build/bazel-release.pub.gpg | gpg --dearmor > bazel.gpg\n", "!sudo mv bazel.gpg /etc/apt/trusted.gpg.d/\n", "!echo \"deb [arch=amd64] https://storage.googleapis.com/bazel-apt stable jdk1.8\" | sudo tee /etc/apt/sources.list.d/bazel.list\n", "!sudo apt update && sudo apt install bazel"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "bEhP3t8TWUD5", "colab_type": "text"}, "source": ["**3. Install a JDK (optional)**"]}, {"cell_type": "markdown", "metadata": {"id": "o5KyantEYXEl", "colab_type": "text"}, "source": ["Sometimes the **default jdk** version may cause an **error** during android sdk installation in ubuntu. So, install an older version of **openjdk-8** and configure the same as the default version of the system."]}, {"cell_type": "code", "metadata": {"id": "u_BoKB459m-L", "colab_type": "code", "colab": {}}, "source": ["!sudo apt install openjdk-8-jdk\n", "!sudo update-alternatives --config java  # Choose OpenJDK 8\n", "!java -version"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "l2pVsBlzWmu5", "colab_type": "text"}, "source": ["**4. Install MediaPipe Without Android Studio (SDK & NDK)**"]}, {"cell_type": "markdown", "metadata": {"id": "WtVc7jSDbAm8", "colab_type": "text"}, "source": ["If the android studio is not installed in your system, you can configute mediapipe with **sdk and ndk** by running this script."]}, {"cell_type": "code", "metadata": {"id": "DLIkgtpRsHDw", "colab_type": "code", "colab": {}}, "source": ["!bash mediapipe/setup_android_sdk_and_ndk.sh"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "486hs2YAbmo8", "colab_type": "text"}, "source": ["**Note:** If Android SDK and NDK are already installed, set **ANDROID_HOME** and **ANDROID_NDK_HOME** paths accordingly.\n", "\n", "```\n", "export ANDROID_HOME=<path to the Android SDK>\n", "export ANDROID_NDK_HOME=<path to the Android NDK>\n", "```\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Rjtp0MGhXMCM", "colab_type": "text"}, "source": ["**5. Generate Slim-Net Tflite**"]}, {"cell_type": "markdown", "metadata": {"id": "0r-bTjNLXzwZ", "colab_type": "text"}, "source": ["Install **tf 1.15** for converting the trained model to tflite fromat."]}, {"cell_type": "code", "metadata": {"id": "GZQXFv1tXRM0", "colab_type": "code", "colab": {}}, "source": ["!pip install tensorflow-gpu==1.15 # Restart kernel, if required"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GhdGl2etYE1o", "colab_type": "text"}, "source": ["Load the trained keras model, and convert it into **tflite** format."]}, {"cell_type": "code", "metadata": {"id": "uOpX1MqbXbBm", "colab_type": "code", "colab": {}}, "source": ["import tensorflow as tf\n", "from tensorflow.keras.models import load_model, Model\n", "\n", "def bilinear_resize(x, rsize):\n", "  return tf.image.resize_bilinear(x, [rsize,rsize], align_corners=True)\n", "\n", "converter = tf.lite.TFLiteConverter.from_keras_model_file('/content/slim-net-157-0.02.hdf5')\n", "tflite_model = converter.convert()\n", "open(\"portrait_segmentation.tflite\", \"wb\").write(tflite_model)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "890b2L2X4qZG", "colab_type": "text"}, "source": ["**6. Modify The Hair Segmentaion Mediapipe Application**\n"]}, {"cell_type": "markdown", "metadata": {"id": "mqo65EH6dwcF", "colab_type": "text"}, "source": ["Set current directory to **mediapipe** and copy the **tflite** file to models directory."]}, {"cell_type": "code", "metadata": {"id": "1d_Ps9fQsIb6", "colab_type": "code", "colab": {}}, "source": ["%cd mediapipe \n", "!cp /content/portrait_segmentation.tflite mediapipe/models/"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "kgEe3PIGfQLg", "colab_type": "text"}, "source": ["**A.** Create a new directory called **portrait_segmentation** under **graphs** subdirectory and copy all the files from **hair_segmentation**."]}, {"cell_type": "code", "metadata": {"id": "byzZ42pQqZAW", "colab_type": "code", "colab": {}}, "source": ["!mkdir mediapipe/graphs/portrait_segmentation\n", "!cp -r mediapipe/graphs/hair_segmentation/* mediapipe/graphs/portrait_segmentation\n", "!mv mediapipe/graphs/portrait_segmentation/hair_segmentation_mobile_gpu.pbtxt mediapipe/graphs/portrait_segmentation/portrait_segmentation_mobile_gpu.pbtxt"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "aUHRFxA4gJ9s", "colab_type": "text"}, "source": ["Rename the pbtxt file(mv command) as **portrait_segmentation_mobile_gpu.pbtxt** and modify the following lines :-\n", "\n", "1. **Number of channels**: 'max_num_channels: 4' in **TfLiteConverterCalculator** to max_num_channels: 3\n", "2. **Model name**: 'hair_segmentaion.tflite' in **TfLiteInferenceCalculator** to portrait_segmentation.tflite\n", "3.  **Mask index**: 'output_layer_index: 1' in **TfLiteTensorsToSegmentationCalculator** to output_layer_index: 0\n", "\n", "**B**. Add two new nodes for **luminance and sobel edge detection** and connect them to the 'throtteld_input_video' as shown below:\n", "\n", "```\n", "# Liminance calculator (RGB)\n", "node {\n", "  calculator: \"LuminanceCalculator\"\n", "  input_stream: \"throttled_input_video\"\n", "  output_stream: \"luma_video\"\n", "}\n", "\n", "# Sobel edge calculator (RGB)\n", "node {\n", "  calculator: \"SobelEdgesCalculator\"\n", "  input_stream: \"luma_video\"\n", "  output_stream: \"sobel_video\"\n", "}\n", "```\n", "\n", "**C.** Now remove the 'RecolorCalculator' node, add **MaskOverlayCalculator**  and connect their inputs as shown below:\n", "\n", "```\n", "# Mask overlay calculator (RGB)\n", "node {\n", "  calculator: \"MaskOverlayCalculator\"\n", "  input_stream: \"VIDEO:0:throttled_input_video\"\n", "  input_stream: \"VIDEO:1:sobel_video\"\n", "  input_stream: \"MASK:hair_mask\"\n", "  output_stream: \"OUTPUT:output_video\"\n", "}\n", "```\n", "**Note:** The idea is to combine the foreground in the image with background using mask, such that the filter is applied only on the background. The '**MaskOverlayCalculator**' nodes combines two input video frames using the mask  such that when mask is 0, **VIDEO:0** will be used and when mask is 1, **VIDEO:1** will be used.Remaining intermediate values (if any) will blend accordingly.\n", "\n", "**D**. Now, inside the **BUILD** file in this directory(portrait_segmentation), change the graph name to \"**portrait_segmentation_mobile_gpu.pbtxt**\".\n", "\n", "Also add the  **calculator files** inside the **cc_library** section for mobile_calculaotrs as follows:-\n", "\n", "```\n", "\"//mediapipe/calculators/image:luminance_calculator\",\n", "\"//mediapipe/calculators/image:sobel_edges_calculator\",\n", "\"//mediapipe/calculators/image:mask_overlay_calculator\",\n", "```\n", "\n", "**E**. <PERSON><PERSON>, create a new folder called '**portraitsegmentationgpu**' inside example directory at location: '**/mediapipe/examples/android/src/java/com/google/mediapipe/apps**'. "]}, {"cell_type": "code", "metadata": {"id": "A2at66eE1h4M", "colab_type": "code", "colab": {}}, "source": ["!mkdir mediapipe/examples/android/src/java/com/google/mediapipe/apps/portraitsegmentationgpu\n", "!cp -r mediapipe/examples/android/src/java/com/google/mediapipe/apps/hairsegmentationgpu/* mediapipe/examples/android/src/java/com/google/mediapipe/apps/portraitsegmentationgpu"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Y0q2feHCqnPR", "colab_type": "text"}, "source": ["Finally, copy the **BUILD** file from hairsegmentationgpu folder into this new folder and **modify** the following lines:-\n", "\n", "1. Change the name 'hairsegmentationgpu' to **portraitsegmentationgpu** everywhere.\n", "2. Change the name 'hair_segmentaion' to **portrait_segmentation** everywhere.\n", "3. Change the app name from 'Hair Segmentation' to **Portait Segmentation**.\n", "\n", "See the final pbtxt file:      [ portrait_segmentation_mobile_gpu.pbtxt](/content/mediapipe/mediapipe/graphs/portrait_segmentation/portrait_segmentation_mobile_gpu.pbtxt)"]}, {"cell_type": "markdown", "metadata": {"id": "5KDB4F1R4oc5", "colab_type": "text"}, "source": ["**7. Build The Android Application Using Bazel**"]}, {"cell_type": "markdown", "metadata": {"id": "SIhdwDgNrwK0", "colab_type": "text"}, "source": ["Build the **portraitsegmentationgpu** android  application for **arm_64** architecture."]}, {"cell_type": "code", "metadata": {"id": "H6xGU_SUttq8", "colab_type": "code", "colab": {}}, "source": ["!bazel build -c opt --config=android_arm64 mediapipe/examples/android/src/java/com/google/mediapipe/apps/portraitsegmentationgpu:portraitsegmentationgpu"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "JOux7DKynwIl", "colab_type": "text"}, "source": ["**Note:** It may take around 20 minutes to complete the build process, for the first time."]}, {"cell_type": "markdown", "metadata": {"id": "EAbyEP1Cr-lG", "colab_type": "text"}, "source": ["Download or save the **apk file** from 'bazel-bin' directory in google drive."]}, {"cell_type": "code", "metadata": {"id": "508TNaaOP6d3", "colab_type": "code", "colab": {}}, "source": ["!cp bazel-bin/mediapipe/examples/android/src/java/com/google/mediapipe/apps/portraitsegmentationgpu/portraitsegmentationgpu.apk /content/drive/My\\ Drive/mediapipe/"], "execution_count": null, "outputs": []}]}